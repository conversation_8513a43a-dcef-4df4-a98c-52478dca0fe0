from src.application.agent.dify_request import DifyRequest
import asyncio


class StudentQuery:

    def __init__(self, user, api_key):
        self.api_key = api_key
        self.user = user

    def send_query(self, scence_question, exercise, student_context):
        dify_request = DifyRequest(self.api_key, self.user, 'StudentQuery')
        param = {
            "scence": scence_question,
            "exercise": exercise,
            'history_dialog': student_context
        }
        student_response = asyncio.run(dify_request.send_conversation(query='.', conversation_id='', inputs=param))
        return student_response.get('answer')
