"""统一的Excel处理器，使用pandas替代openpyxl

这个模块提供了标准化的Excel处理接口，支持：
- 统一的Excel文件读取和解析
- 多种数据格式的导出
- 高性能的数据处理
- 类型安全的数据操作
"""

import logging
from io import BytesIO
from typing import Dict, List, Any, Optional, Union, Tuple

import pandas as pd
from flask import send_file

logger = logging.getLogger(__name__)


class ExcelProcessor:
    """统一的Excel处理器基类
    
    提供标准化的Excel文件处理接口，使用pandas进行高效的数据操作。
    """
    
    def __init__(self):
        """初始化Excel处理器"""
        self.data: Optional[pd.DataFrame] = None
        self.sheets: Dict[str, pd.DataFrame] = {}
    
    def load_file(self, file, sheet_name: Union[str, int, List[str], None] = None) -> 'ExcelProcessor':
        """加载Excel文件
        
        Args:
            file: 文件对象或文件路径
            sheet_name: 工作表名称，None表示加载所有工作表
            
        Returns:
            self: 支持链式调用
        """
        try:
            if sheet_name is None:
                # 加载所有工作表
                self.sheets = pd.read_excel(file, sheet_name=None, engine='openpyxl')
                # 默认使用第一个工作表作为主数据
                if self.sheets:
                    self.data = list(self.sheets.values())[0]
            else:
                # 加载指定工作表
                if isinstance(sheet_name, (str, int)):
                    self.data = pd.read_excel(file, sheet_name=sheet_name, engine='openpyxl')
                    self.sheets[str(sheet_name)] = self.data
                else:
                    # 加载多个工作表
                    self.sheets = pd.read_excel(file, sheet_name=sheet_name, engine='openpyxl')
                    if self.sheets:
                        self.data = list(self.sheets.values())[0]
            
            logger.info(f"成功加载Excel文件，包含 {len(self.sheets)} 个工作表")
            return self
            
        except Exception as e:
            logger.error(f"加载Excel文件失败: {e}")
            raise
    
    def get_sheet(self, sheet_name: str) -> Optional[pd.DataFrame]:
        """获取指定工作表的数据
        
        Args:
            sheet_name: 工作表名称
            
        Returns:
            工作表数据或None
        """
        return self.sheets.get(sheet_name)
    
    def get_data(self) -> Optional[pd.DataFrame]:
        """获取主数据
        
        Returns:
            主数据DataFrame
        """
        return self.data
    
    def get_rows_as_list(self, skip_header: bool = True) -> List[List[Any]]:
        """将数据转换为行列表格式
        
        Args:
            skip_header: 是否跳过表头
            
        Returns:
            行数据列表
        """
        if self.data is None:
            return []
        
        if skip_header:
            return self.data.values.tolist()
        else:
            # 包含表头
            return [self.data.columns.tolist()] + self.data.values.tolist()
    
    def get_columns(self) -> List[str]:
        """获取列名列表
        
        Returns:
            列名列表
        """
        if self.data is None:
            return []
        return self.data.columns.tolist()
    
    def filter_data(self, condition) -> 'ExcelProcessor':
        """过滤数据
        
        Args:
            condition: pandas查询条件
            
        Returns:
            self: 支持链式调用
        """
        if self.data is not None:
            self.data = self.data[condition]
        return self
    
    def select_columns(self, columns: List[str]) -> 'ExcelProcessor':
        """选择指定列
        
        Args:
            columns: 列名列表
            
        Returns:
            self: 支持链式调用
        """
        if self.data is not None:
            self.data = self.data[columns]
        return self
    
    def add_column(self, name: str, values: Union[List, pd.Series, Any]) -> 'ExcelProcessor':
        """添加新列
        
        Args:
            name: 列名
            values: 列值
            
        Returns:
            self: 支持链式调用
        """
        if self.data is not None:
            self.data[name] = values
        return self
    
    def export_to_excel(self, headers: Optional[List[str]] = None, 
                       data: Optional[List[List[Any]]] = None) -> bytes:
        """导出为Excel文件字节数据
        
        Args:
            headers: 表头列表，如果为None则使用当前数据的列名
            data: 数据列表，如果为None则使用当前数据
            
        Returns:
            Excel文件字节数据
        """
        try:
            # 准备数据
            if data is not None:
                # 使用提供的数据
                if headers:
                    df = pd.DataFrame(data, columns=headers)
                else:
                    df = pd.DataFrame(data)
            else:
                # 使用当前数据
                df = self.data if self.data is not None else pd.DataFrame()
            
            # 导出到字节流
            buffer = BytesIO()
            with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='Sheet1')
            
            buffer.seek(0)
            return buffer.getvalue()
            
        except Exception as e:
            logger.error(f"导出Excel失败: {e}")
            raise
    
    def export_to_flask_response(self, filename: str = 'export.xlsx', 
                                headers: Optional[List[str]] = None,
                                data: Optional[List[List[Any]]] = None):
        """导出为Flask响应对象
        
        Args:
            filename: 文件名
            headers: 表头列表
            data: 数据列表
            
        Returns:
            Flask send_file响应
        """
        excel_data = self.export_to_excel(headers, data)
        buffer = BytesIO(excel_data)
        
        return send_file(
            buffer,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    
    @staticmethod
    def create_from_data(data: List[List[Any]], headers: Optional[List[str]] = None) -> 'ExcelProcessor':
        """从数据列表创建处理器实例
        
        Args:
            data: 数据列表
            headers: 表头列表
            
        Returns:
            ExcelProcessor实例
        """
        processor = ExcelProcessor()
        if headers:
            processor.data = pd.DataFrame(data, columns=headers)
        else:
            processor.data = pd.DataFrame(data)
        return processor
    
    def get_legacy_format(self) -> Dict[str, Any]:
        """获取兼容旧格式的数据结构
        
        Returns:
            包含columns和rows的字典，兼容旧的analyze_excel_by_row格式
        """
        if self.data is None:
            return {'columns': [], 'rows': []}
        
        return {
            'columns': self.get_columns(),
            'rows': self.get_rows_as_list(skip_header=True)
        }


class DialogExcelProcessor(ExcelProcessor):
    """对话场景专用的Excel处理器
    
    处理包含多个工作表的复杂Excel文件，如基础、变量、测试场景等。
    """
    
    def parse_dialog_excel(self, file) -> Dict[str, Any]:
        """解析对话场景的Excel文件
        
        Args:
            file: Excel文件
            
        Returns:
            解析后的数据结构
        """
        self.load_file(file)
        
        try:
            base_sheet = self.get_sheet('基础')
            variable_sheet = self.get_sheet('变量')
            scene_sheet = self.get_sheet('测试场景')
            
            if base_sheet is None or variable_sheet is None or scene_sheet is None:
                raise ValueError("缺少必要的工作表：基础、变量、测试场景")
            
            # 读取基础数据
            session_num = base_sheet.iloc[0, 0]  # A1
            cycle_num = base_sheet.iloc[0, 1]    # B1
            
            # 读取变量数据
            exercise = variable_sheet.iloc[0, 0]      # A1
            solution_steps = variable_sheet.iloc[0, 1] # B1
            concept = variable_sheet.iloc[0, 2]        # C1
            
            # 读取场景数据（跳过表头）
            scene_data = scene_sheet.iloc[1:].values.tolist()
            
            return {
                "session_num": session_num,
                "cycle_num": cycle_num,
                "exercise": exercise,
                "solution_steps": solution_steps,
                "scence_data": scene_data,
                "concept": concept
            }
            
        except Exception as e:
            logger.error(f"解析对话Excel文件失败: {e}")
            raise


class SimpleExcelProcessor(ExcelProcessor):
    """简单Excel处理器
    
    处理单一工作表的简单Excel文件。
    """
    
    def parse_simple_excel(self, file, sheet_name: str = 'Sheet1') -> List[List[Any]]:
        """解析简单的Excel文件
        
        Args:
            file: Excel文件
            sheet_name: 工作表名称
            
        Returns:
            行数据列表（跳过表头）
        """
        self.load_file(file, sheet_name)
        return self.get_rows_as_list(skip_header=True)


# 兼容性函数，保持向后兼容
def analyze_excel_by_row(file) -> Dict[str, Any]:
    """兼容性函数：解析Excel文件为行格式
    
    Args:
        file: Excel文件
        
    Returns:
        包含columns和rows的字典
    """
    processor = ExcelProcessor()
    processor.load_file(file)
    return processor.get_legacy_format()


def export_excel(headers: List[str], data: List[List[Any]]) -> bytes:
    """兼容性函数：导出Excel文件
    
    Args:
        headers: 表头列表
        data: 数据列表
        
    Returns:
        Excel文件字节数据
    """
    processor = ExcelProcessor.create_from_data(data, headers)
    return processor.export_to_excel()