﻿import asyncio
import logging
import traceback
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from random import randint
from threading import Lock
from typing import List, Dict, Any, Optional, Tuple, Union

from src.application.agent.dify_request import DifyRequest
from src.application.error.Exception import RetryException
from src.application.tool.excel_util import analyze_excel_by_row
from src.application.tool.excel_util import export_excel

# 初始化蓝图

logger = logging.getLogger()


def cycle_export_by_excel(file, api_key: str, max_workers: int = 2) -> bytes:
    """使用多线程异步处理Excel文件导出
    
    Args:
        file: Excel文件
        api_key: API密钥
        max_workers: 最大工作线程数，默认5
        
    Returns:
        导出的Excel文件字节数据
    """
    analyze_data = analyze_excel_by_row(file)
    logger.info('解析后的文件内容:%s', analyze_data)
    columns = analyze_data['columns']
    rows = analyze_data['rows']
    # 模糊用户id
    user = randint(100000, 999999)
    datas = []
    data_lock = Lock()  # 用于保护共享数据
    conversation_id = ''
    
    # 使用线程池执行器进行并发处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_index = {}
        for i, row in enumerate(rows):
            future = executor.submit(
                _process_row_with_retry,
                conversation_id, i + 2, row, columns, user, api_key
            )
            future_to_index[future] = i + 2
        
        # 收集结果
        for future in as_completed(future_to_index):
            row_index = future_to_index[future]
            try:
                result = future.result()
                if result is not None:
                    with data_lock:
                        datas.append(result)
                        logger.info(f"第{row_index}行处理完成")
            except Exception as e:
                logger.error(f"第{row_index}行处理失败: {e}")
                traceback.print_exc()
    
    # 按下标排序确保顺序正确
    datas.sort(key=lambda x: int(x[0]))
    headers = ["下标", "答复"]
    return export_excel(headers=headers, data=datas)


def cycle_export_workflow_by_excel(file, api_key: str, max_workers: int = 2) -> bytes:
    """使用多线程异步处理工作流Excel文件导出
    
    Args:
        file: Excel文件
        api_key: API密钥
        max_workers: 最大工作线程数，默认5
        
    Returns:
        导出的Excel文件字节数据
    """
    analyze_data = analyze_excel_by_row(file)
    logger.info('解析后的文件内容:%s', analyze_data)
    columns = analyze_data['columns']
    rows = analyze_data['rows']
    # 模糊用户id
    user = randint(100000, 999999)
    datas = []
    data_lock = Lock()  # 用于保护共享数据
    all_headers = None
    
    # 先处理第一行获取表头
    if rows:
        try:
            result, headers = _process_workflow_row_with_retry(
                0 + 2, rows[0], columns, user, api_key, first_row=True
            )
            if result is not None:
                all_headers = list(headers)
                datas.append(result)
                logger.info("第一行处理完成，获取表头")
        except Exception as e:
            logger.error(f"第一行处理失败: {e}")
            traceback.print_exc()
            return export_excel(headers=[], data=[])
    
    # 如果有多行数据，使用多线程处理剩余行
    if len(rows) > 1:
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交剩余任务
            future_to_index = {}
            for i, row in enumerate(rows[1:], 1):  # 从第二行开始
                future = executor.submit(
                    _process_workflow_row_with_retry,
                    i + 2, row, columns, user, api_key, 
                    first_row=False, all_headers=all_headers
                )
                future_to_index[future] = i + 2
            
            # 收集结果
            for future in as_completed(future_to_index):
                row_index = future_to_index[future]
                try:
                    result = future.result()
                    if result is not None:
                        with data_lock:
                            datas.append(result)
                            logger.info(f"第{row_index}行处理完成")
                except Exception as e:
                    logger.error(f"第{row_index}行处理失败: {e}")
                    traceback.print_exc()
    
    # 转换数据格式以适应excel导出
    excel_data = []
    # 按行索引排序确保顺序正确
    datas.sort(key=lambda x: int(x.get('行索引', 0)) if '行索引' in x else 0)
    
    # 添加数据行
    for row_data in datas:
        row = []
        for header in all_headers:
            row.append(row_data.get(header, ''))
        excel_data.append(row)

    return export_excel(headers=all_headers, data=excel_data)


def _process_row_with_retry(conversation_id: str, i: int, row: List[Any], 
                           columns: List[str], user: int, api_key: str) -> Optional[List[str]]:
    """带重试机制的行处理函数
    
    Args:
        conversation_id: 对话ID
        i: 行索引
        row: 行数据
        columns: 列名
        user: 用户ID
        api_key: API密钥
        
    Returns:
        处理结果或None
    """
    error_cycle = 0
    retry_index = 0
    
    while retry_index < 3:
        try:
            return _do_handle(conversation_id, i, row, columns, user, api_key)
        except RetryException:
            retry_index += 1
            logger.info(f"第{i}行发生异常，当前重试次数: {retry_index}")
        except Exception:
            error_cycle += 1
            if error_cycle > 4:
                logger.error(f"第{i}行处理失败，超过最大错误次数")
                break
            traceback.print_exc()
    
    return None


def _do_handle(conversation_id: str, i: int, row: List[Any], 
              columns: List[str], user: int, api_key: str) -> Optional[List[str]]:
    """处理单行数据
    
    Args:
        conversation_id: 对话ID
        i: 行索引
        row: 行数据
        columns: 列名
        user: 用户ID
        api_key: API密钥
        
    Returns:
        处理结果或None
    """
    param = {}
    for index in range(len(row)):
        if index < len(columns):
            column_name = columns[index]
            # 保持原始列名作为参数名，确保与API接口兼容
            param[column_name] = str(row[index] or '')
    
    # 检查所有param的值是否都为空
    if all(not value.strip() for value in param.values()):
        logger.info(f"第{i}行所有参数值为空，跳过处理")
        return None
        
    dify_request = DifyRequest(user=user, api_key=api_key, name=api_key)
    query = param.get('对话框输入')
    if '对话框输入' in param:
        param.pop('对话框输入')
    
    response = asyncio.run(
        dify_request.send_conversation(query=query, conversation_id=conversation_id,
                                       inputs=param,
                                       response_mode='streaming'))
    
    return [str(i), response.get('answer', '')]


def _process_workflow_row_with_retry(i: int, row: List[Any], columns: List[str], 
                                    user: int, api_key: str, first_row: bool = False, 
                                    all_headers: Optional[List[str]] = None) -> Union[Optional[Tuple[Dict[str, str], List[str]]], Optional[Dict[str, str]]]:
    """带重试机制的工作流行处理函数
    
    Args:
        i: 行索引
        row: 行数据
        columns: 列名
        user: 用户ID
        api_key: API密钥
        first_row: 是否为第一行
        all_headers: 所有表头
        
    Returns:
        处理结果或None
    """
    error_cycle = 0
    retry_index = 0
    
    while retry_index < 3:
        try:
            return _do_handle_workflow(i, row, columns, user, api_key, first_row, all_headers)
        except RetryException:
            retry_index += 1
            logger.info(f"第{i}行发生异常，当前重试次数: {retry_index}")
        except Exception:
            error_cycle += 1
            if error_cycle > 3:
                logger.error(f"第{i}行处理失败，超过最大错误次数")
                break
            traceback.print_exc()
    
    return None


def _do_handle_workflow(i: int, row: List[Any], columns: List[str], 
                       user: int, api_key: str, first_row: bool = False, 
                       all_headers: Optional[List[str]] = None) -> Union[Optional[Tuple[Dict[str, str], List[str]]], Optional[Dict[str, str]]]:
    """处理单行工作流数据
    
    Args:
        i: 行索引
        row: 行数据
        columns: 列名
        user: 用户ID
        api_key: API密钥
        first_row: 是否为第一行
        all_headers: 所有表头
        
    Returns:
        处理结果或None
    """
    param = {}
    for index in range(len(row)):
        param.setdefault(columns[index], str(row[index] or ''))
    
    # 检查所有param的值是否都为空
    if all(not value.strip() for value in param.values()):
        logger.info(f"第{i}行所有参数值为空，跳过处理")
        return None
        
    dify_request = DifyRequest(user=user, api_key=api_key, name=api_key)
    response = asyncio.run(
        dify_request.send_request(inputs=param, url="https://platform-dify-api-fat.61info.cn/v1/workflows/run",
                                  response_mode="blocking"))

    answer = response.get('data', {}).get('outputs', {})

    # 如果是第一行，需要收集表头
    if first_row:
        # 使用列名值而不是索引，并过滤掉纯数字的列名
        headers = []
        for idx in sorted(columns.keys()):
            col_name = columns[idx]
            # 如果列名是纯数字，则替换为更有意义的名称
            if col_name.isdigit():
                headers.append(f"输入{int(col_name)+1}")
            else:
                headers.append(col_name)
        
        # 添加API返回的新字段
        if isinstance(answer, dict):
            for key in answer.keys():
                if key not in headers:
                    headers.append(key)
        
        # 添加行索引用于排序（如果不存在）
        if '行索引' not in headers:
            headers.append('行索引')
    else:
        headers = all_headers or []

    # 处理数据
    result = {}
    # 使用所有已知的表头创建结果字典，初始值为空字符串
    for header in headers:
        result[header] = ''

    # 添加原有的输入参数
    for index in range(len(row)):
        if index < len(columns):
            column_name = columns[index]
            # 如果列名是纯数字，使用与表头处理一致的命名
            if column_name.isdigit():
                result_key = f"输入{int(column_name)+1}"
            else:
                result_key = column_name
            result[result_key] = str(row[index] or '')
    
    # 添加API返回的新字段
    if isinstance(answer, dict):
        for key, value in answer.items():
            result[key] = str(value or '')
    
    # 添加行索引用于后续排序
    result['行索引'] = str(i)

    if first_row:
        return result, headers
    else:
        return result
