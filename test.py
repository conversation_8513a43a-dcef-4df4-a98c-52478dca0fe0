import json
def main(video_extract: str) -> dict:
    ## 将json字符串转换为dict
    video_extract = json.loads(video_extract)
    arr = []
    for frame, url in video_extract['frames'].items():
        arr.append('http://127.0.0.1:5000' + video_extract['base_url'] + url)
    return arr

if __name__ == '__main__':
    print(main('''{"base_url": "/static/frames/", "frames": {"1": "frame_1s_b8f69efa.jpg", "2": "frame_2s_2ed42807.jpg", "40": "frame_40s_218ba069.jpg"}, "success": true}''')) 
