import logging
import traceback
from random import randint

from openpyxl import load_workbook

from src.application.agent.solution_procedure.procedure import Procedure
from src.application.error.Exception import RetryException
from src.application.tool.excel_util import export_excel

# 初始化蓝图

logger = logging.getLogger()


def generate(file):
    analyze_data = analyze_excel(file)
    logger.info('解析后的文件内容:%s', analyze_data)
    user = '11111'

    # 模糊用户id
    user = randint(100000, 999999)
    datas = []
    conversation_id = ''
    for i, row in enumerate(analyze_data):
        retry_index = 0
        while retry_index < 3:
            try:
                do_handle(conversation_id, datas, i, row, user)
                break
            except RetryException:
                retry_index = retry_index + 1
                logger.info("发生异常，当前重试次数:%s", retry_index)
            # 某一步异常不影响后续流程
            except Exception:
                traceback.print_exc()
        # 添加表头
    headers = ["下标", "答复"]
    return export_excel(data=datas, headers=headers)


def do_handle(conversation_id, datas, i, row, user):
    data = [None, None]
    input_data = str(row[0])
    example_a = str(row[1] or '')
    example = str(row[2] or '')
    s = str(row[3] or '')
    number = row[4]
    reflect = str(row[5] or '')
    reflect2 = str(row[6] or '')
    instruct = str(row[7] or '')
    teacher_analyze = Procedure(user, 'app-TGQd7rCGyhsGnMoHfXyHHeQW')
    response = teacher_analyze.send_query(conversation_id, input_data, example_a, example, s, reflect, reflect2,
                                          instruct)
    data[0] = str(i)
    data[1] = response.get('answer')
    datas.append(data)


def analyze_excel(file):
    """解析excel文件，封装成指定的数据结构."""

    # 读取 Excel 文件
    workbook = load_workbook(file)

    sheet = workbook['Sheet1']
    # 读取数据
    rows = []
    for index, row in enumerate(sheet.iter_rows(values_only=True)):
        if index == 0:
            continue
        rows.append(row)
    return rows
