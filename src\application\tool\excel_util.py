import logging
import time
from io import BytesIO

from flask import send_file
from openpyxl.reader.excel import load_workbook
from openpyxl.workbook import Workbook

logger = logging.getLogger()


def analyze_excel_by_row(file):
    """解析excel文件，封装成指定的数据结构.
    
    Args:
        file: Excel文件对象
        
    Returns:
        dict: 包含rows和columns的字典
            - rows: 数据行列表（不包含表头行）
            - columns: 列名字典，键为列索引，值为列名
    """
    # 读取 Excel 文件
    workbook = load_workbook(file)

    sheet = workbook['Sheet1']
    # 读取数据
    rows = []
    columns = {}
    for index, row in enumerate(sheet.iter_rows(values_only=True)):
        if index == 0:
            # 处理表头行，将列名存储在字典中
            for row_index in range(len(row)):
                # 确保列名是字符串类型
                col_name = str(row[row_index]) if row[row_index] is not None else f"列{row_index+1}"
                columns[row_index] = col_name
            continue
        rows.append(row)
    
    return {"rows": rows, "columns": columns}


def export_excel(headers, data):
    wb = Workbook()
    ws = wb.active
    ws.append(headers)

    # 添加数据
    for i, item in enumerate(data):
        logger.info('打印excel数据第%s行,data: %s', i, item)
        ws.append(item)

    # 创建一个内存文件对象
    excel_buffer = BytesIO()
    wb.save(excel_buffer)
    excel_buffer.seek(0)

    # 返回 Excel 文件
    return send_file(
        excel_buffer,
        as_attachment=True,
        download_name=str(time.time()) + '.xlsx',
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
