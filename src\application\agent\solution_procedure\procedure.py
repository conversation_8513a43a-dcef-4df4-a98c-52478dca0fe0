import asyncio
import logging

from src.application.agent.dify_request import DifyRequest
from src.application.error.Exception import RetryException

logger = logging.getLogger()


class Procedure:
    def __init__(self, user, api_key):
        self.api_key = api_key
        self.user = user

    def send_query(self, conversation_id, query, example_a, example, s, reflect, reflect2, instruct):
        dify_request = DifyRequest(self.api_key, self.user, 'Procedure')
        param = {
            "example_a": example_a,
            'example': example,
            'S': s,
            'reflect': reflect,
            'reflect2': reflect2,
            'instruct': instruct,
        }
        student_response = asyncio.run(
            dify_request.send_conversation(query=query, conversation_id=conversation_id, inputs=param,
                                           response_mode='streaming'))
        return student_response

    def send_query_v2(self, conversation_id, query, example_a, example, s, reflect, reflect2, instruct, Example_a_image,
                      Example_image):
        retry_index = 0
        while retry_index < 3:
            try:
                dify_request = DifyRequest(self.api_key, self.user, 'ProcedureV2')
                param = {
                    "example_a": example_a,
                    'example': example,
                    'S': s,
                    'reflect': reflect,
                    'reflect2': reflect2,
                    'instruct': instruct,
                    'Example_a_image': Example_a_image,
                    'Example_image': Example_image,
                }
                student_response = asyncio.run(
                    dify_request.send_conversation(query=query, conversation_id=conversation_id, inputs=param,
                                                   response_mode='streaming'))
                return student_response
            except RetryException:
                retry_index = retry_index + 1
                logger.info("发生异常，当前重试次数:%s", retry_index)
        return None
