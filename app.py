from flask import Flask, send_from_directory, render_template
from flask_cors import CORS
from api import app as api_app
import src.application.log.log_handler as log
import os

app = Flask(__name__, static_folder='/templates')
CORS(app, supports_credentials=True)

# 解决中文乱码问题
app.json.ensure_ascii = False
app.register_blueprint(api_app, url_prefix='/')


@app.route('/static/')
def list_files():
    # List files from the uploads directory
    files = os.listdir('src')
    return render_template('/chat.html', files=files)


@app.route('/static/<path:filename>')
def serve_file(filename):
    # Serve the file from the uploads directory
    return send_from_directory('templates', filename)


if __name__ == '__main__':
    log.init()
    app.run(debug=False, host='0.0.0.0', port='5000')
