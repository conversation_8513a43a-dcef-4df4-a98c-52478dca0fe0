import asyncio
import logging
import re
import traceback
import urllib.parse
from random import randint

import cachetools
from openpyxl import load_workbook

from src.application.agent.question.student_query import StudentQuery
from src.application.agent.question.student_query_check import <PERSON><PERSON><PERSON>y<PERSON>heck
from src.application.agent.question.teacher_answer import TeacherAnalyze
from src.application.agent.question.teacher_answer import TeacherAnswer
from src.application.agent.question.teacher_answer import TeacherProcess
from src.application.tool.excel_util import export_excel

# 初始化蓝图
# 创建 LRU 缓存
history_dialog_cache = cachetools.LRUCache(maxsize=1000)
last_process_cache = cachetools.LRUCache(maxsize=1000)
logger = logging.getLogger()

def chat(file):
    analyze_data = analyze_excel(file)
    logger.info('解析后的文件内容:%s', analyze_data)
    scence_data = analyze_data['scence_data']

    exercise = analyze_data['exercise']
    solution_steps = analyze_data['solution_steps']
    cycle_num = analyze_data['cycle_num']
    session_num = analyze_data['session_num']
    concept = analyze_data['concept']
    # 模糊用户id
    user = randint(100000, 999999)
    data = []
    for session_index in range(session_num):
        logger.info('----------第%s次会话----------', session_index + 1)
        teacher_conversation_id = ''
        history_dialog = ''
        current_cycle_num = 0
        # 起手第一轮不记录在内
        cycle_num += 1
        while True:
            try:
                logger.info('第%s次循环', current_cycle_num)
                if current_cycle_num >= cycle_num:
                    break
                # 提前+1，避免student_query异常造成死循环无法退出
                mark_cycle_num = current_cycle_num
                current_cycle_num += 1
                # 每次都进行random
                parse_scence = random_scence_data(scence_data)
                scence_question = parse_scence['scence_question']
                scence = parse_scence['scence']
                scence_type = parse_scence['scence_type']
                student_query_check = ''
                if current_cycle_num == 1:
                    query = "这道题怎么做？"
                    scence = "请求帮助或澄清问题"
                    scence_type = "解题相关"
                else:
                    max_index = 2
                    for query_index in range(max_index):
                        student = StudentQuery(user, 'app-S9CTVFG3EoycDG8HSaEHmeTK')
                        query = student.send_query(scence_question, exercise, history_dialog)
                        student_check = StudentQueryCheck(user, 'app-pHAUckwqYZwaJrworO5KxXcC')
                        student_query_check = student_check.check_query(query, scence_question, history_dialog,
                                                                        exercise)
                        if '不符合要求' in student_query_check and query_index < max_index - 1:
                            excel_data = prepare_excel_data(exercise, solution_steps, mark_cycle_num,
                                                            scence_type, scence, scence_question, query,
                                                            session_index + 1, {}, history_dialog, student_query_check)
                            data.append(excel_data)
                        else:
                            break
                teacher_answer = TeacherAnswer(user, 'app-5toGLriOjTIRWKrM0AUdKOxx')
                teacher_response = teacher_answer.teacher_query(teacher_conversation_id, query, solution_steps,
                                                                exercise, concept)
                teacher_conversation_id = teacher_response.get('conversation_id')
                # 网络超时，重置会话
                if teacher_conversation_id is None:
                    teacher_conversation_id = ''
                teacher_parse_data = teacher_answer.teacher_parse(teacher_response.get('answer'))

                excel_data = prepare_excel_data(exercise, solution_steps, mark_cycle_num,
                                                scence_type, scence,
                                                scence_question, query, session_index + 1,
                                                teacher_parse_data, history_dialog, student_query_check, concept)
                history_dialog = append_student_context(history_dialog, query, teacher_parse_data)

                data.append(excel_data)

                if '学生已理解最后一步' in teacher_parse_data.get('process'):
                    break
            # 某一步异常不影响后续流程
            except Exception:
                traceback.print_exc()
                # 添加表头
    headers = ["会话ID", "{题目}", "{解题步骤}", "{知识点}", "轮次", "场景分类", "场景",
               "场景要求", "历史对话", "学生query", "query符合要求(分析过程)", "query符合要求", "意图分析", "进度分析",
               "回应策略", "具体回复"]
    return export_excel(data=data, headers=headers)


def chat_v2(file):
    analyze_data = analyze_excel(file)
    logger.info('解析后的文件内容:%s', analyze_data)
    scence_data = analyze_data['scence_data']

    exercise = analyze_data['exercise']
    solution_steps = analyze_data['solution_steps']
    cycle_num = analyze_data['cycle_num']
    session_num = analyze_data['session_num']
    concept = analyze_data['concept']
    # 模糊用户id
    user = randint(100000, 999999)
    data = []
    for session_index in range(session_num):
        logger.info('----------第%s次会话----------', session_index + 1)
        teacher_conversation_id = ''
        analyze_conversation_id = ''
        process_conversation_id = ''
        history_dialog = ''
        current_cycle_num = 0
        # 起手第一轮不记录在内
        cycle_num += 1
        while True:
            try:
                logger.info('第%s次循环', current_cycle_num)
                if current_cycle_num >= cycle_num:
                    break
                # 提前+1，避免student_query异常造成死循环无法退出
                mark_cycle_num = current_cycle_num
                current_cycle_num += 1
                # 每次都进行random
                parse_scence = random_scence_data(scence_data)
                scence_question = parse_scence['scence_question']
                scence = parse_scence['scence']
                scence_type = parse_scence['scence_type']
                student_query_check = ''
                if current_cycle_num == 1:
                    query = "这道题怎么做？"
                    scence = "请求帮助或澄清问题"
                    scence_type = "解题相关"
                else:
                    max_index = 2
                    for query_index in range(max_index):
                        student = StudentQuery(user, 'app-S9CTVFG3EoycDG8HSaEHmeTK')
                        query = student.send_query(scence_question, exercise, history_dialog)
                        student_check = StudentQueryCheck(user, 'app-pHAUckwqYZwaJrworO5KxXcC')
                        student_query_check = student_check.check_query(query, scence_question, history_dialog,
                                                                        exercise)
                        if '不符合要求' in student_query_check and query_index < max_index - 1:
                            excel_data = prepare_excel_data(exercise, solution_steps, mark_cycle_num,
                                                            scence_type, scence, scence_question, query,
                                                            session_index + 1, {}, history_dialog, student_query_check)
                            data.append(excel_data)
                        else:
                            break

                # 意图分析+进度分析
                v2_chat = v2_process(user, teacher_conversation_id, analyze_conversation_id, process_conversation_id,
                                     query, exercise, solution_steps, history_dialog, concept)
                teacher_conversation_id = v2_chat.get('teacher_conversation_id')
                analyze_conversation_id = v2_chat.get('analyze', []).get('conversation_id')
                process_conversation_id = v2_chat.get('process', []).get('conversation_id')
                # 网络超时，重置会话
                if teacher_conversation_id is None:
                    teacher_conversation_id = ''
                if analyze_conversation_id is None:
                    analyze_conversation_id = ''
                if process_conversation_id is None:
                    process_conversation_id = ''
                # 覆盖值，单独agent获取
                teacher_parse_data = v2_chat.get('teacher_parse')
                teacher_parse_data['analyze'] = v2_chat.get('analyze', []).get('answer')
                teacher_parse_data['process'] = v2_chat.get('process', []).get('answer')

                excel_data = prepare_excel_data(exercise, solution_steps, mark_cycle_num,
                                                scence_type, scence,
                                                scence_question, query, session_index + 1,
                                                teacher_parse_data, history_dialog, student_query_check, concept)
                history_dialog = append_student_context(history_dialog, query, teacher_parse_data)

                data.append(excel_data)

                if '学生已理解最后一步' in teacher_parse_data.get('process'):
                    break
            # 某一步异常不影响后续流程
            except Exception:
                traceback.print_exc()
    headers = ["会话ID", "{题目}", "{解题步骤}", "{知识点}", "轮次", "场景分类", "场景",
               "场景要求", "历史对话", "学生query", "query符合要求(分析过程)", "query符合要求", "意图分析", "进度分析",
               "回应策略", "具体回复"]
    return export_excel(data=data, headers=headers)


def chat_message(data):
    exercise = urllib.parse.unquote(data['exercise'])
    solution_steps = urllib.parse.unquote(data['solution_steps'])
    query = data['query']
    concept = data['concept']
    conversation_id = data.get('conversation_id')
    history_dialog = ''
    if conversation_id is not None and conversation_id != '':
        history_dialog = history_dialog_cache.get(conversation_id)
    if history_dialog is None:
        history_dialog = ''

    v2_chat = v2_process('11111222', conversation_id, '', '',
                         query, exercise, solution_steps, history_dialog, concept)
    teacher_conversation_id = v2_chat.get('teacher_conversation_id')
    teacher_parse = v2_chat.get('teacher_parse')
    teacher_parse['analyze'] = v2_chat.get('analyze', []).get('answer')
    teacher_parse['process'] = v2_chat.get('process', []).get('answer')
    result = {
        'conversation_id': teacher_conversation_id,
        'teacher_parse': teacher_parse,
        'history_dialog': history_dialog
    }
    history_dialog = append_student_context(history_dialog, query, teacher_parse)
    history_dialog_cache[teacher_conversation_id] = history_dialog
    return result


def v2_process(user, teacher_conversation_id, analyze_conversation_id, process_conversation_id,
               query, exercise, solution_steps, history_dialog, concept):
    """
    v2 流程
    1.意图分析
    2.流程分析
    3.老师回答
    notice：12可并行
    :param process_conversation_id:
    :param analyze_conversation_id:
    :param user: 用户id
    :param teacher_conversation_id: 老师agent对话id
    :param query: 学生问题
    :param exercise: 习题
    :param solution_steps: 解题步骤
    :param history_dialog: 历史对话
    :return:
    """
    if teacher_conversation_id is None or teacher_conversation_id == '':
        last_process_pre = None
    else:
        last_process_pre = last_process_cache.get(teacher_conversation_id)
    if last_process_pre is None:
        last_process_pre = '未开始'
    # 意图分析 + 进度分析
    result = asyncio.run(async_run(user, analyze_conversation_id, process_conversation_id,
                                   query, exercise, solution_steps, history_dialog, last_process_pre))
    analyze_response = result[0]
    process_response = result[1]
    teacher_answer = TeacherAnswer(user, 'app-9Acy2ADFdglNE5Ea31cnmer8')
    # 只截取需要的部分
    process_answer = process_response.get('answer')

    teacher_response = teacher_answer.teacher_v2_query(teacher_conversation_id, query, solution_steps,
                                                       exercise, analyze_response.get('answer'),
                                                       process_answer, concept)
    teacher_conversation_id = teacher_response.get('conversation_id')
    teacher_parse_data = teacher_answer.teacher_parse(teacher_response.get('answer'))
    process_answer_reg = re.search(r'接下来，需本次重点讲解的步骤编号(.*)', process_answer)
    if process_answer_reg:
        last_process_group = process_answer_reg.group()
        last_process_cache[teacher_conversation_id] = last_process_group.replace('接下来，需本次重点讲解的步骤编号：', '')
    return {
        'analyze': analyze_response,
        'process': process_response,
        'teacher_parse': teacher_parse_data,
        'teacher_conversation_id': teacher_conversation_id
    }


async def async_run(user, analyze_conversation_id, process_conversation_id,
                    query, exercise, solution_steps, history_dialog, last_process):
    teacher_analyze = TeacherAnalyze(user, 'app-WaKH3Jk2w0dGantgZBqUhcO0')
    teacher_process = TeacherProcess(user, 'app-YajChqwog0oNGCCMFwEYv0qI')
    return await asyncio.gather(teacher_analyze.analyze('', query, exercise, solution_steps, history_dialog),
                                teacher_process.process('', query, exercise, solution_steps,
                                                        history_dialog, last_process))


def random_scence_data(scence_data):
    total = 0
    decimal_point = 10000
    # 不足4位小数，等比例放大
    for row in scence_data:
        total += row[4]
    for i, row in enumerate(scence_data):
        row = list(row)
        row[4] = row[4] * (decimal_point / total)
        scence_data[i] = row
    ladder_num = 0
    random_digit = randint(0, decimal_point)
    for i, row in enumerate(scence_data):
        ladder_num += row[4]
        # 避免小数不足
        if i == len(scence_data) - 1:
            ladder_num = decimal_point
        logger.info('random_num:%s ladder_num:%s', random_digit, ladder_num)
        if random_digit <= ladder_num:
            scence_type = row[1]
            scence = row[2]
            scence_question = row[3]
            logger.info('概率选择的是:%s 序号:%s', row[4], row[0])
            break
    data = {
        "scence_type": scence_type,
        "scence": scence,
        "scence_question": scence_question
    }
    return data


def append_student_context(context, query, teacher_parse_data):
    if context is None:
        context = ''
    context += '【学生】：' + query + '\n'
    context += '【老师】：' + \
               teacher_parse_data.get('reply').replace('\n\n', '') + '\n'
    return context


def prepare_excel_data(exercise, step, cycle_num, scence_type, scence, scence_question,
                       query, teacher_conversation_id, teacher_response_parses, history_dialog, student_query_check,
                       concept):
    data = [None] * 16
    data[0] = teacher_conversation_id
    data[1] = exercise
    data[2] = step
    data[3] = concept
    data[4] = cycle_num
    data[5] = scence_type
    data[6] = scence
    data[7] = scence_question
    data[8] = history_dialog
    data[9] = query
    if "'''" in student_query_check:
        split_str = student_query_check.split("'''")
        data[10] = split_str[0]
        data[11] = split_str[1]
    elif isinstance(student_query_check, dict):
        data[10] = ''
        data[11] = ''
    else:
        data[10] = student_query_check
        data[11] = ''
    data[12] = teacher_response_parses.get('analyze')
    data[13] = teacher_response_parses.get('process')
    data[14] = teacher_response_parses.get('ponder')
    data[15] = teacher_response_parses.get('reply')
    return data


def analyze_excel(file):
    """解析excel文件，封装成指定的数据结构."""

    # 读取 Excel 文件
    workbook = load_workbook(file)

    base_sheet = workbook['基础']
    variable_sheet = workbook['变量']
    scence_sheet = workbook['测试场景']
    # 读取数据
    scence_data = []
    for index, row in enumerate(scence_sheet.iter_rows(values_only=True)):
        if index == 0:
            continue
        scence_data.append(row)

    session_num = base_sheet['A'][1].value
    cycle_num = base_sheet['B'][1].value
    exercise = variable_sheet['A'][1].value
    solution_steps = variable_sheet['B'][1].value
    concept = variable_sheet['C'][1].value
    result = {
        "session_num": session_num,
        "cycle_num": cycle_num,
        "exercise": exercise,
        "solution_steps": solution_steps,
        "scence_data": scence_data,
        "concept": concept
    }
    return result
