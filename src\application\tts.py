import traceback
import httpx
import json
import logging

logger = logging.getLogger(__name__)
# 填写平台申请的appid, access_token以及cluster
appid = 2731290883
access_token = 'ldK_lM_PYe4syTEGKFulcw2tHeZsOwio'
cluster = 'volcano_tts'
api_url = 'https://openspeech.bytedance.com/api/v1/tts'

header = {"Authorization": f"Bearer;{access_token}"}

hs_request_json = {
    "app": {
        "appid": appid,
        "token": access_token,
        "cluster": cluster
    }
}


async def generate_audio_data(param_dict):
    hs_request_json.update(param_dict)
    print(hs_request_json)
    # 尝试3次
    try_count = 3
    msg = ""
    result = {}
    data_key = "data"
    message_key = "message"
    while try_count > 0:
        try:
            async with httpx.AsyncClient() as client:
                resp = await client.post(api_url, data=json.dumps(hs_request_json), headers=header, timeout=30)
            resp_d = resp.json()
            result[data_key] = resp_d[data_key]
            result[message_key] = resp_d[message_key]
            return result
        except Exception as e:
            logger.error(f"调用火山TTS接口失败,剩余尝试次数={try_count}: {traceback.format_exc()}")
            try_count -= 1
            msg = str(e)
    result[message_key] = msg
    result[data_key] = ''
    return result
