import logging
import time

from src.application.agent.dify_request import DifyRequest
import asyncio
logger = logging.getLogger()


class TeacherAnswer:
    def __init__(self, user, api_key):
        self.api_key = api_key
        self.user = user

    def teacher_query(self, conversation_id, query, solution_steps, exercise, concept):
        dify_request = DifyRequest(self.api_key, self.user, 'TeacherAnswer')
        param = {
            "solution_steps": solution_steps,
            "exercise": exercise,
            "concept": concept
        }
        teacher_response = asyncio.run(
            dify_request.send_conversation(query=query, conversation_id=conversation_id, inputs=param))
        return teacher_response

    def teacher_v2_query(self, conversation_id, query, solution_steps, exercise, intention, process, concept):
        dify_request = DifyRequest(self.api_key, self.user, 'TeacherAnswer')
        param = {
            "solution_steps": solution_steps,
            "exercise": exercise,
            "intention": intention,
            "process": process,
            "concept": concept
        }
        teacher_response = asyncio.run(
            dify_request.send_conversation(query=query, conversation_id=conversation_id, inputs=param,
                                           response_mode='streaming',
                                           interceptor=self.catchEffective))
        return teacher_response

    def teacher_v2_check_query(self, conversation_id, query, solution_steps, exercise, intention, process, concept,
                               history_assistant, strategy):
        dify_request = DifyRequest(self.api_key, self.user, 'TeacherAnswer')
        param = {
            "solution_steps": solution_steps,
            "exercise": exercise,
            "intention": intention,
            "process": process,
            "concept": concept
        }
        param.update(history_assistant)
        param.update(strategy)
        teacher_response = asyncio.run(
            dify_request.send_conversation(query=query, conversation_id=conversation_id, inputs=param,
                                           response_mode='streaming',
                                           interceptor=self.catchEffective))
        return teacher_response

    def catchEffective(self, start_time, content, burial_point):
        if '具体回复' in content and 'end_time' not in burial_point:
            burial_point['end_time'] = time.time() - start_time
        return burial_point

    def teacher_parse(self, answer):
        if answer is None or '教学策略' not in answer:
            analyze = ''
            process = ''
            ponder = ''
            reply = answer
        # elif "'''" not in answer:
        #     analyze = re.search("意图识别.*\n", answer).group()
        #     process = re.search("进度分析.*\n", answer).group()
        #     ponder = re.search("教学策略.*\n", answer).group()
        #     reply = re.search("具体回复[\s\S]*", answer).group().replace('%%%', '').replace(
        #         '具体回复：', '').replace('\n\n\n\n', '').replace('\n\n\n', '')
        else:
            analyze = ''
            process = ''
            ponder = ''
            reply = ''
            split_strs = answer.split("'''")
            for split_str in split_strs:
                if '意图识别' in split_str:
                    analyze = split_str.replace('意图识别\n', '')
                    continue
                if '进度分析' in split_str:
                    split_str = split_str.replace('进度分析\n', '')
                    process = split_str
                    continue
                if '教学策略' in split_str:
                    ponder = split_str.replace('教学策略\n', '')
                    continue
                if '具体回复' in split_str:
                    split_str = split_str.replace('具体回复\n', '').replace(
                        '\n\n\n\n', '').replace('\n\n\n', '')
                    reply = split_str
                    continue
        data = {
            "analyze": analyze,
            "process": process,
            "ponder": ponder,
            "reply": reply
        }
        logger.info('老师解析内容:%s', data)
        return data


class TeacherAnalyze:
    def __init__(self, user, api_key):
        self.api_key = api_key
        self.user = user

    async def analyze(self, conversation_id, query, exercise, solution_steps, history_dialog,
                      response_mode: str = 'blocking'):
        dify_request = DifyRequest(self.api_key, self.user, 'TeacherAnalyze')
        param = {
            "history_dialog": history_dialog,
            "exercise": exercise,
            "solution_steps": solution_steps
        }
        teacher_response = await dify_request.send_conversation(query=query, conversation_id=conversation_id,
                                                                inputs=param, response_mode=response_mode)
        return teacher_response


class TeacherProcess:
    def __init__(self, user, api_key):
        self.api_key = api_key
        self.user = user

    async def process(self, conversation_id, query, exercise, solution_steps, history_dialog,
                      last_process, response_mode: str = 'blocking'):
        dify_request = DifyRequest(self.api_key, self.user, 'TeacherProcess')
        param = {
            "solution_steps": solution_steps,
            "history_dialog": history_dialog,
            "exercise": exercise,
            "last_process": last_process
        }
        teacher_response = await dify_request.send_conversation(query=query, conversation_id=conversation_id,
                                                                inputs=param, response_mode=response_mode)
        return teacher_response
