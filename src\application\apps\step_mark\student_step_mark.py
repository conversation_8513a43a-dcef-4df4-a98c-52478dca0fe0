﻿import asyncio
import logging
import re
from random import randint

from src.application.agent.dify_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.application.agent.question.teacher_answer import TeacherAnalyze, TeacherProcess, TeacherAnswer
from src.application.tool.cycle_util import cycle_handle_without_conversation
from src.application.tool.excel_util import analyze_excel_by_row, export_excel

# 初始化蓝图

logger = logging.getLogger()


def cycle_analyze_by_excel(file):
    analyze_data = analyze_excel_by_row(file)
    logger.info('解析后的文件内容:%s', analyze_data)
    columns = analyze_data['columns']
    rows = analyze_data['rows']
    # 模糊用户id
    user = randint(100000, 999999)
    params = {
        'columns': columns
    }
    datas = cycle_handle_without_conversation(rows=rows, params=params, user=user, handle=do_analyze_handle)
    headers = ["exercise", "solution_steps", "history_dialog", "学生最新回复", "学生的意图", "老师输出意图",
               "首token耗时", "完整输出耗时", "usage", "人工准确判断"]
    return export_excel(data=datas, headers=headers)


def cycle_process_by_excel(file):
    analyze_data = analyze_excel_by_row(file)
    logger.info('解析后的文件内容:%s', analyze_data)
    columns = analyze_data['columns']
    rows = analyze_data['rows']
    # 模糊用户id
    user = randint(100000, 999999)
    conversation_id = ''
    params = {
        'columns': columns
    }
    datas = cycle_handle_without_conversation(rows=rows, params=params, user=user, handle=do_process_handle)
    headers = ["exercise", "solution_steps", "history_dialog", "学生最新回复", "<last_process>", "老师输出进度分析",
               "首token耗时", "完整输出耗时", "usage", "综合评估打分", "学习进度分析评估（原因）",
               "本次重点讲解的步骤编号一致性判断（打分）", "本次重点讲解的步骤编号一致性判断（原因）"]
    return export_excel(data=datas, headers=headers)


def cycle_teacher_by_excel(file, teacher_api_key):
    analyze_data = analyze_excel_by_row(file)
    logger.info('解析后的文件内容:%s', analyze_data)
    columns = analyze_data['columns']
    rows = analyze_data['rows']
    # 模糊用户id
    user = randint(100000, 999999)
    params = {
        'columns': columns,
        'teacher_api_key': teacher_api_key
    }
    datas = cycle_handle_without_conversation(rows=rows, params=params, user=user, handle=do_teacher_handle)
    headers = ["exercise", "solution_steps", "concept", "学生意图", "进度", "学生最新回复",
               "history_dialog", "老师输出教学策略", "老师输出具体回复", "首token耗时", "首字符耗时",
               "完整输出耗时", "usage", "回应范围初阶确认 - 指标满足分级", "回应范围进阶确认 - 指标满足分级",
               "讲解初阶增强 - 指标满足分级",
               "讲解进阶增强 - 指标满足分级", "回复总结 - 指标满足分级", "回应范围初阶确认 - 分析过程",
               "回应范围进阶确认 - 分析过程",
               "讲解初阶增强 - 分析过程", "讲解进阶增强 - 分析过程", "回复总结 - 分析过程", "按实际意图回复",
               "按分析意图回复", "教纲范围",
               "结果正确", "步骤命中", "有效讲解", "分步解答", "不透露答案", "信息可靠", "内容健康", "语言逻辑",
               "难度适宜", "语言流畅", "输出要求",
               "按实际意图回复 - 分析过程", "按分析意图回复 - 分析过程", "教纲范围 - 分析过程",
               "结果正确 - 分析过程", "步骤命中 - 分析过程",
               "有效讲解 - 分析过程", "分步解答 - 分析过程", "不透露答案 - 分析过程", "信息可靠 - 分析过程",
               "内容健康 - 分析过程", "语言逻辑 - 分析过程",
               "难度适宜 - 分析过程", "语言流畅- 分析过程", "输出要求 - 分析过程"]
    return export_excel(data=datas, headers=headers)


def do_teacher_handle(row, params, user):
    param = {}
    for index in range(len(row)):
        param.setdefault(params.get('columns')[index], str(row[index] or ''))
    teacher_api_key = params.get('teacher_api_key')
    history_dialog_batch = param.get('history_dialog')
    matches = re.findall(r'【学生】：(.*?)[\n|\\n]*【老师】：(.*?)[\n|\\n]+', history_dialog_batch)
    logger.info(f"正则解析老师学生历史对话记录:{matches}")
    history_assistant = {}
    if len(matches) == 0:
        logger.error(f"解析history_dialog异常,请核实数据:{history_dialog_batch}")
    else:
        max_index = 5
        begin = 1
        for i, (student, teacher) in enumerate(matches, 1):
            if len(matches) > max_index:
                if len(matches) - i >= max_index - 1:
                    continue
            history_assistant['user' + str(begin)] = student
            history_assistant['assistant' + str(begin)] = teacher
            begin = begin + 1
    history_dialog = param.get('history_dialog')
    query = param.get('学生最新回复')
    datas = [do_teacher_answer_parse(history_dialog=history_dialog, history_assistant=history_assistant,
                                     param=param, query=query, user=user, teacher_api_key=teacher_api_key)]
    return datas


def do_teacher_answer_parse(history_dialog, history_assistant, param, query, user, teacher_api_key):
    teacher_answer = TeacherAnswer(user=user, api_key=teacher_api_key)
    teacher_agent_strategy = {}
    for i in range(4):
        key = 'strategy' + str(i)
        strategy_n = param.get(key)
        if strategy_n is None:
            continue
        teacher_agent_strategy[key] = strategy_n
    teacher_answer_response = teacher_answer.teacher_v2_check_query(conversation_id='', query=query,
                                                                    solution_steps=param.get('solution_steps'),
                                                                    exercise=param.get('exercise'),
                                                                    intention=param.get('学生意图'),
                                                                    concept=param.get('concept'),
                                                                    process=param.get('进度'),
                                                                    history_assistant=history_assistant,
                                                                    strategy=teacher_agent_strategy)
    answer = teacher_answer_response.get('answer')
    if '教学策略' in answer:
        answer_matches = re.search("'''教学策略([\s\S]*?)'''具体回复([\s\S]*)", answer)
        (teach_strategy, teach_relpy) = answer_matches.groups()
    else:
        teach_strategy = ''
        teach_relpy = answer
    teach_strategy_judge_parse = None
    if teach_strategy is not None and teach_strategy != '':
        teach_strategy_judge = DifyHandler(user=user, api_key='app-mANXA5t666Kwah6VceVic0Na', name='TeachStrategyJudge')
        teach_params = {
            'situation': param.get('学生意图'),
            'student_query': query,
            'history_dialog': history_dialog,
            'step_analysis': param.get('进度'),
            'exercise': param.get('exercise'),
            'solution': param.get('solution_steps'),
            'strategy': teach_strategy
        }
        teach_strategy_judge_response = asyncio.run(teach_strategy_judge.send_request(teach_params))
        teach_strategy_judge_answer = teach_strategy_judge_response.get('data').get('outputs').get('LLM')
        teach_strategy_judge_parse = re.search(
            "\|判断<豌豆精灵确认的回应策略>是否符合标准【回应范围确认（初阶）】标准：([\s\S]*?)\|判断<豌豆精灵确认的回应策略>是否符合标准【回应范围确认（进阶）】标准：([\s\S]*?)\|判断<豌豆精灵确认的回应策略>是否符合标准【讲解增强（初阶）】标准：([\s\S]*?)\|判断<豌豆精灵确认的回应策略>是否符合标准【讲解增强（进阶）】标准：([\s\S]*?)\|判断<豌豆精灵确认的回应策略>是否符合标准【总结】标准：([\s\S]*)",
            teach_strategy_judge_answer).groups()
        logger.info(f"策略解析{teach_strategy_judge_parse}")

    teach_relpy_judge = DifyHandler(user=user, api_key='app-cOBr5KdBQGxJ0wo6PJdRP4Kf', name='TeachRelpyJudge')
    relpy_params = {
        'situation': param.get('学生意图'),
        'student_query': query,
        'history_dialog': history_dialog,
        'step_analysis': param.get('进度'),
        'exercise': param.get('exercise'),
        'solution': param.get('solution_steps'),
        'answer': teach_relpy
    }
    teach_reply_judge_response = asyncio.run(teach_relpy_judge.send_request(relpy_params))
    teach_reply_judge_answer = teach_reply_judge_response.get('data').get('outputs').get('LLM')
    teach_relpy_judge_parse = parse_relpy(teach_reply_judge_answer)
    logger.info(f"回答解析:{teach_relpy_judge_parse}")
    return prepare_teacher_excel_data(history_dialog=history_dialog, input_params=param,
                                      query=query,
                                      teacher_answer_response=teacher_answer_response,
                                      teach_strategy=teach_strategy,
                                      teach_relpy=teach_relpy, strategy_judge=teach_strategy_judge_parse,
                                      relpy_judge=teach_relpy_judge_parse)


def prepare_teacher_excel_data(history_dialog, query, input_params, teacher_answer_response, teach_strategy,
                               teach_relpy, strategy_judge, relpy_judge):
    data = [None] * 51
    data[0] = input_params.get('exercise')
    data[1] = input_params.get('solution_steps')
    data[2] = input_params.get('concept')
    data[3] = input_params.get('学生意图')
    data[4] = input_params.get('进度')
    data[5] = query
    data[6] = history_dialog
    data[7] = teach_strategy
    data[8] = teach_relpy
    data[9] = teacher_answer_response.get('first_token_time')
    data[10] = "{:.2f}".format(float(str(teacher_answer_response.get('burial_point').get('end_time') or 0)))
    data[11] = teacher_answer_response.get('completed_time')
    data[12] = str(teacher_answer_response.get('usage'))
    reply_elementary = ['', ''] if strategy_judge is None else parse_teach_strategy(strategy_judge[0])
    reply_enhance = ['', ''] if strategy_judge is None else parse_teach_strategy(strategy_judge[1])
    explain_elementary = ['', ''] if strategy_judge is None else parse_teach_strategy(strategy_judge[2])
    explain_enhance = ['', ''] if strategy_judge is None else parse_teach_strategy(strategy_judge[3])
    summary = ['', ''] if strategy_judge is None else parse_teach_strategy(strategy_judge[4])
    data[13] = reply_elementary[1]
    data[14] = reply_enhance[1]
    data[15] = explain_elementary[1]
    data[16] = explain_enhance[1]
    data[17] = summary[1]
    data[18] = reply_elementary[0]
    data[19] = reply_enhance[0]
    data[20] = explain_elementary[0]
    data[21] = explain_enhance[0]
    data[22] = summary[0]
    data[23] = relpy_judge[1]
    data[24] = relpy_judge[3]
    data[25] = relpy_judge[5]
    data[26] = relpy_judge[7]
    data[27] = relpy_judge[9]
    data[28] = relpy_judge[11]
    data[29] = relpy_judge[13]
    data[30] = relpy_judge[15]
    data[31] = relpy_judge[17]
    data[32] = relpy_judge[19]
    data[33] = relpy_judge[21]
    data[34] = relpy_judge[23]
    data[35] = relpy_judge[25]
    data[36] = relpy_judge[27]
    data[37] = relpy_judge[0]
    data[38] = relpy_judge[2]
    data[39] = relpy_judge[4]
    data[40] = relpy_judge[6]
    data[41] = relpy_judge[8]
    data[42] = relpy_judge[10]
    data[43] = relpy_judge[12]
    data[44] = relpy_judge[14]
    data[45] = relpy_judge[16]
    data[46] = relpy_judge[18]
    data[47] = relpy_judge[20]
    data[48] = relpy_judge[22]
    data[49] = relpy_judge[24]
    data[50] = relpy_judge[26]
    return data


def do_process_handle(row, params, user):
    param = {}
    for index in range(len(row)):
        param.setdefault(params.get('columns')[index], str(row[index] or ''))
    teacher_process = TeacherProcess(user, 'app-uaiEj5cCt9PvRO9WJjikxYj5')
    teacher_process_response = asyncio.run(
        teacher_process.process(query=param.get('学生最新回复'), exercise=param.get('exercise'),
                                solution_steps=param.get('solution_steps'),
                                history_dialog=param.get('history_dialog'),
                                last_process=param.get('last_process'),
                                response_mode='streaming', conversation_id=''))
    dify_handler = DifyHandler(user=user, api_key='app-hBKZ0ecmhUm85JimS5LzUc3W', name='ProcessJudge')
    input_data = {
        'exercise': param.get('exercise'),
        'solution_steps': param.get('solution_steps'),
        'history_dialog': param.get('history_dialog'),
        'studen_query': param.get('学生最新回复'),
        'step_analysis': teacher_process_response.get('answer')
    }
    judge_response = asyncio.run(dify_handler.send_request(input_data))
    judge_answer = parse_step_mark_answer(judge_response.get('data').get('outputs').get('裁判打分'))
    logger.info(f'裁判打分正则解析为:{judge_answer}')
    return [prepare_process_excel_data(param=param, process_response=teacher_process_response,
                                       judge_answer=judge_answer)]


def prepare_process_excel_data(param, process_response, judge_answer):
    data = [None] * 13
    data[0] = param.get('exercise')
    data[1] = param.get('solution_steps')
    data[2] = param.get('history_dialog')
    data[3] = param.get('学生最新回复')
    data[4] = param.get('last_process')
    data[5] = process_response.get('answer')
    data[6] = process_response.get('first_token_time')
    data[7] = process_response.get('completed_time')
    data[8] = str(process_response.get('usage'))
    data[9] = str(judge_answer.get('synthesis_score'))
    data[10] = judge_answer.get('process_reason')
    data[11] = str(judge_answer.get('consistency_score'))
    data[12] = judge_answer.get('consistency_reason')
    return data


def parse_step_mark_answer(answer):
    matches = re.search(
        r'\|学习进度分析评估：([\s\S]*?)\| (\d)[\n]+\|本次重点讲解的步骤编号一致性判断：([\s\S]*?)\| (\d)[\n]+\|综合评估：([\s\S]*?)\| (\d)',
        answer)
    if matches:
        (process_evaluate, process_score, consistency_reason,
         consistency_score, synthesis_reason, synthesis_score) = matches.groups()
        return {'process_reason': process_evaluate,
                'process_score': process_score,
                'consistency_reason': consistency_reason,
                'consistency_score': consistency_score,
                'synthesis_reason': synthesis_reason,
                'synthesis_score': synthesis_score}
    return {}


def do_analyze_handle(row, params, user):
    param = {}
    for index in range(len(row)):
        param.setdefault(params.get('columns')[index], str(row[index] or ''))
    teacher_analyze = TeacherAnalyze(user, 'app-LCi2zUILpfGB3dGpgkeTqvq6')
    query = param.get('学生最新回复')
    analyze_response = asyncio.run(
        teacher_analyze.analyze(conversation_id='', query=query, exercise=param.get('exercise'),
                                history_dialog=param.get('history_dialog'),
                                solution_steps=param.get('solution_steps'), response_mode='streaming')
    )
    return [prepare_analyze_excel_data(param=param, analyze_response=analyze_response)]


def prepare_analyze_excel_data(param, analyze_response):
    match_flag = 0
    analyze_answer = analyze_response.get('answer')
    process_answer_reg = re.search(r'：(.*)]', analyze_answer)
    if process_answer_reg:
        llm_intention = process_answer_reg.group(1)
        excel_intention = param.get('学生的意图').split('+')
        if llm_intention in excel_intention:
            match_flag = 1
    data = [None] * 10
    data[0] = param.get('exercise')
    data[1] = param.get('solution_steps')
    data[2] = param.get('history_dialog')
    data[3] = param.get('学生最新回复')
    data[4] = param.get('学生的意图')
    data[5] = analyze_answer
    data[6] = analyze_response.get('first_token_time')
    data[7] = analyze_response.get('completed_time')
    data[8] = str(analyze_response.get('usage'))
    data[9] = str(match_flag)
    return data


def append_student_context(student_str, teacher_str):
    context_str = ''
    context_str += '【学生】：' + student_str + '\n'
    context_str += '【老师】：' + teacher_str + '\n'
    return context_str


def parse_teach_strategy(param):
    return re.search("\|分析过程：([\s\S]*?)\|评分结果：\|(\d)\|", param).groups()


def parse_relpy(param):
    """|【按实际意图回复】|学生表达了对之前讲解的否定，老师的回复针对学生的疑惑重新进行讲解，符合解题场景的回复要求。|1|
|【按分析意图回复】|老师的分析是“[表达否定：解题相关]”，老师的回复针对学生的疑惑重新进行讲解，符合解题场景的回复要求。|1|
|【教纲范围】|老师的讲解内容均在<解题步骤>中出现。|1|
|【结果正确】|老师的计算过程和结果均正确。|1|
|【步骤命中】|学生要求重新解释，老师的回复涵盖了步骤5、7、8的内容，符合步骤命中的要求。|1|
|【有效讲解】|老师的回复完整地解释了计算思路和过程，讲解有效。|2|
|【分步解答】|老师在回复中为了帮助学生理解提及了历史步骤中的多个步骤，符合分步解答的要求。|1|
|【不透露答案】|老师的回复中包含步骤5、7、8的答案，但这些答案在之前的对话中已经出现，因此不视为透露答案。|1|
|【信息可靠】|提供的信息可靠。|1|
|【内容健康】|内容健康。|1|
|【语言逻辑】|逻辑清晰，表达流畅。|1|
|【难度适宜】|用词简单，讲解清晰，难度适宜。|2|
|【语言流畅】|语言流畅，表达清晰。|1|
|【输出要求】|符合输出要求。|1|"""
    return re.search(
        """\|【按实际意图回复】\|([\s\S]*?)\|([\s\S])\|\n\|【按分析意图回复】\|([\s\S]*?)\|([\s\S])\|\n\|【教纲范围】\|([\s\S]*?)\|([\s\S])\|\n\|【结果正确】\|([\s\S]*?)\|([\s\S])\|\n\|【步骤命中】\|([\s\S]*?)\|([\s\S])\|\n\|【有效讲解】\|([\s\S]*?)\|([\s\S])\|\n\|【分步解答】\|([\s\S]*?)\|([\s\S])\|\n\|【不透露答案】\|([\s\S]*?)\|([\s\S])\|\n\|【信息可靠】\|([\s\S]*?)\|([\s\S])\|\n\|【内容健康】\|([\s\S]*?)\|(\d)\|\n\|【语言逻辑】\|([\s\S]*?)\|([\s\S])\|\n\|【难度适宜】\|([\s\S]*?)\|([\s\S])\|\n\|【语言流畅】\|([\s\S]*?)\|([\s\S])\|\n\|【输出要求】\|([\s\S]*?)\|([\s\S])\|""",
        param).groups()


if __name__ == '__main__':
    history_dialog_batch = '【学生】：这道题怎么做？\\n【老师】：小明，我们先从题目中寻找等量关系开始。题目中提到：“水果店运来的西瓜个数是哈密瓜个数的2倍”。我们把这句翻译成数学式子可以怎么写呢？可以尝试一下吗？\\n【学生】：我在学校做过类似的题目了。\\n【老师】：小明，很好啊！你之前在学校也做过类似的题目呢。那我们一起来看看这道题吧，首先想一想题目中有什么样的等量关系呢？\\n'
    #print(re.findall(r'【学生】：(.*?)[\n|\\n]*【老师】：(.*?)[\n|\\n]+', history_dialog_batch))
    # print(parse_relpy("""|【"意图识别"遵循】|学生表达了理解，老师也对此进行了积极回应，符合要求|1|\n|【"进度分析"遵循】|<老师判断的讲解进度>表示应该讲解Step7，实际具体回复中也在引导学生思考Step7的内容，符合要求|1|\n|【"教学策略"遵循】|<老师分析的教学策略>为空，无需评分|1|\n|【教纲范围】|讲解内容均可在<解题步骤>Step7中找到，不超纲|1|\n|【分步解答】|仅引导学生思考Step7的内容，符合一步解答要求|1|\n|【不透露答案】|未直接告诉学生答案，而是通过提问引导学生思考|1|\n|【结果正确】|引导内容与<解题步骤>一致，无错误|1|\n|【有效回答】|针对性地引导学生思考等式性质，有效推进解题|2|\n|【信息可靠】|内容基于基础数学原理，可靠|1|\n|【内容健康】|无不当内容|1|\n|【难度适宜】|使用简单词汇，概念容易理解|2|\n|【语言流畅】|表达清晰连贯|1|\n|【语言逻辑】|逻辑顺序合理，从肯定到引导|1|\n|【输出要求】|无数学公式，回复简短，符合要求|1|"""))
    print(parse_relpy('|【"意图识别"遵循】|学生意图是表达理解，老师的回复确认了学生的理解，并引导进行下一步，符合要求|1|\n|【"进度分析"遵循】|讲解进度判断为Step 7，老师的回复引导进行下一步计算，符合讲解进度要求|1|\n|【"教学策略"遵循】|无教学策略要求，此项无需评估|-|\n|【教纲范围】|老师的回复内容均在<解题步骤>中，符合要求|1|\n|【分步解答】|老师的回复只涉及下一步的引导，没有进行具体讲解，符合分步解答的要求|1|\n|【不透露答案】|老师的回复没有透露答案，只是引导学生进行下一步计算，符合要求|1|\n|【结果正确】|老师的回复没有涉及具体计算，无结果错误|1|\n|【有效回答】|老师的回复有效地回应了学生的理解，并引导进行下一步，属于有效解答|2|\n|【信息可靠】|无事实性错误|1|\n|【内容健康】|无少儿不宜内容|1|\n|【难度适宜】|使用了简单的词汇和表达，9岁孩子可以理解|2|\n|【语言流畅】|语法正确，表达连贯清晰|1|\n|【语言逻辑】|逻辑一致，过渡自然|1|\n|【输出要求】|无数学公式，无需使用LaTeX格式，回复简短，符合要求|1|\n'))
    pass
