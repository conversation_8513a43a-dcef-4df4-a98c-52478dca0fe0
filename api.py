﻿import logging
import time
import uuid
import asyncio
import json
import os
import requests
from flask import request, Blueprint, jsonify
from src.application.apps.solution_procedure import generate as solution_procedure
from src.application.apps.step_mark.student_step_mark import cycle_analyze_by_excel, cycle_process_by_excel, \
    cycle_teacher_by_excel
from src.application.apps.student_question import dialog as student_question
from src.application.apps.step_mark import step_mark as step_mark
from src.application.tts import generate_audio_data
from src.application.volcengine_asr import run_asr
from src.application.apps.cycle.cycle_export import cycle_export_by_excel, cycle_export_workflow_by_excel
from src.application.video_processor import VideoProcessor
from flask_cors import cross_origin, CORS

app = Blueprint('app', __name__)
logger = logging.getLogger()


@app.post("/generate/")
def solution_procedure():
    if 'file' not in request.files:
        return "No file part"
    file = request.files['file']
    # 如果用户没有选择文件，则浏览器也会提交一个空的部分，没有文件名
    if file.filename == '':
        return "No selected file"
    return solution_procedure.generate(file=file)


@app.post('/dialog/chat')
def chat():
    if 'file' not in request.files:
        return "No file part"
    file = request.files['file']
    # 如果用户没有选择文件，则浏览器也会提交一个空的部分，没有文件名
    if file.filename == '':
        return "No selected file"
    return student_question.chat(file=file)


@app.post('/dialog/chat-v2')
def chat_v2():
    if 'file' not in request.files:
        return "No file part"
    file = request.files['file']
    # 如果用户没有选择文件，则浏览器也会提交一个空的部分，没有文件名
    if file.filename == '':
        return "No selected file"
    return student_question.chat_v2(file=file)


@app.post('/dialog/chat-message')
def chat_message():
    data = request.get_json()
    return student_question.chat_message(data=data)


@app.post('/step_mark/')
def mark():
    if 'file' not in request.files:
        return "No file part"
    file = request.files['file']
    # 如果用户没有选择文件，则浏览器也会提交一个空的部分，没有文件名
    if file.filename == '':
        return "No selected file"
    return step_mark.mark(file=file)


@app.post('/export_agent/')
def export_agent():
    if 'file' not in request.files:
        return "No file part"
    file = request.files['file']
    # 如果用户没有选择文件，则浏览器也会提交一个空的部分，没有文件名
    if file.filename == '':
        return "No selected file"
    api_key = request.form.get('api_key')
    return cycle_export_by_excel(file=file, api_key=api_key)


@app.post('/export_analyze_judge/')
def export_analyze_judge():
    if 'file' not in request.files:
        return "No file part"
    file = request.files['file']
    # 如果用户没有选择文件，则浏览器也会提交一个空的部分，没有文件名
    if file.filename == '':
        return "No selected file"
    return cycle_analyze_by_excel(file=file)


@app.post('/export_process_judge/')
def export_process_judge():
    if 'file' not in request.files:
        return "No file part"
    file = request.files['file']
    # 如果用户没有选择文件，则浏览器也会提交一个空的部分，没有文件名
    if file.filename == '':
        return "No selected file"
    return cycle_process_by_excel(file=file)


@app.post('/export_teacher_judge/')
def export_teacher_judge():
    if 'file' not in request.files:
        return "No file part"
    file = request.files['file']
    # 如果用户没有选择文件，则浏览器也会提交一个空的部分，没有文件名
    if file.filename == '':
        return "No selected file"
    return cycle_teacher_by_excel(file=file, teacher_api_key='app-9Acy2ADFdglNE5Ea31cnmer8')


@app.post('/export_teacher_judge_v2/')
def export_teacher_judge_v2():
    if 'file' not in request.files:
        return "No file part"
    file = request.files['file']
    # 如果用户没有选择文件，则浏览器也会提交一个空的部分，没有文件名
    if file.filename == '':
        return "No selected file"
    api_key = request.form.get('teacher_api_key')
    return cycle_teacher_by_excel(file=file, teacher_api_key=api_key)


@app.post('/export_workflow/')
def export_workflow():
    if 'file' not in request.files:
        return "No file part"
    file = request.files['file']
    # 如果用户没有选择文件，则浏览器也会提交一个空的部分，没有文件名
    if file.filename == '':
        return "No selected file"
    api_key = request.form.get('api_key')
    return cycle_export_workflow_by_excel(file=file, api_key=api_key)


@app.post('/video/extract-frames')
def extract_frames():
    """
    视频抽帧API接口
    
    请求参数:
    - video_url: 视频URL地址
    - seconds: 需要抽取的秒数列表，JSON格式，例如 [1, 5, 10]
    - start_second: 开始时间（秒）
    - end_second: 结束时间（秒）
    
    返回:
    {
        "success": true,
        "frames": {
            "1": "frame_1s_abcd1234.jpg",
            "5": "frame_5s_efgh5678.jpg",
            "10": "frame_10s_ijkl9012.jpg"
        },
        "base_url": "/static/frames/"
    }
    """
    try:
        data = request.get_json()
        if not data or 'video_url' not in data:
            return jsonify({"success": False, "error": "Missing video_url parameter"}), 400
            
        video_url = data.get('video_url')
        if not video_url:
            return jsonify({"success": False, "error": "Empty video_url"}), 400
        
        # 保存从URL下载的视频文件
        temp_dir = "templates/temp"
        os.makedirs(temp_dir, exist_ok=True)
        is_rename = data.get('is_rename', True)
        video_path = os.path.join(temp_dir, is_rename and f"{uuid.uuid4().hex}.mp4" or f"{video_url.split('/')[-1]}")
        # 是否存在该文件
        if not os.path.exists(video_path):
            # 下载视频文件
            try:
                
                response = requests.get(video_url, stream=True)
                response.raise_for_status()
                
                with open(video_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
            except Exception as e:
                return jsonify({"success": False, "error": f"Failed to download video: {str(e)}"}), 400
        
        # 获取需要抽取的秒数
        seconds = data.get('seconds', [])
        
        # 如果提供了开始和结束时间，则使用区间抽帧
        if 'start_second' in data and 'end_second' in data:
            try:
                start_second = int(data['start_second'])
                end_second = int(data['end_second'])
                frames = VideoProcessor.extract_frames_by_interval(
                    video_path, start_second, end_second
                )
            except ValueError:
                return jsonify({"success": False, "error": "Invalid start or end second"}), 400
        else:
            # 使用指定秒数抽帧
            if not seconds:
                return jsonify({"success": False, "error": "No seconds specified"}), 400
                
            frames = VideoProcessor.extract_frames_at_seconds(video_path, seconds)
        
        # 将结果中的整数键转换为字符串，以便JSON序列化
        frames_str_keys = {str(k): v for k, v in frames.items()}
             # 删除临时视频文件
        if is_rename:
            os.remove(video_path)
            
        return jsonify({
            "success": True,
            "frames": frames_str_keys,
            "base_url": "/static/frames/"
        })
        
    except Exception as e:
        logger.error(f"视频抽帧失败: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500