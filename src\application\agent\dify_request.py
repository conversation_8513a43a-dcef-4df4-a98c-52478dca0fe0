import json
import logging
import time

import requests
from requests.adapters import HTTPAdapter
import aiohttp
import uuid
from src.application.error.Exception import RetryException

s = requests.Session()
s.mount('http://', HTTPAdapter(max_retries=3))
s.mount('https://', HTTPAdapter(max_retries=3))
logger = logging.getLogger()


class DifyRequest:

    def __init__(self, api_key, user, name):
        self.api_key = api_key
        self.user = user
        self.name = name

    async def send_request(self, url="https://api.dify.ai/v1/workflows/run", response_mode: str = 'blocking',
                           inputs=None):
        """发送工作流请求"""
        headers = {
            'Authorization': 'Bearer ' + self.api_key,
            'Content-Type': 'application/json ; charset=utf-8',
        }
        payload = {
            "inputs": inputs,
            "response_mode": response_mode,
            "user": self.user
        }
        try:
            req_uuid = uuid.uuid1()
            logger.info('name:%s uuid:%s 聊天机器人对话参数:%s', self.name, req_uuid, payload)
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, headers=headers) as response:
                    if 'blocking' in response_mode:
                        return await self.send_request_handle(req_uuid, response)
                    elif 'streaming' in response_mode:
                        return await self.send_request_stream(req_uuid, response)
        except Exception as e:
            logger.error('name:%s uuid:%s http调用失败,避免中断程序', self.name, uuid, e)
            return {}

    async def send_request_handle(self, req_uuid, response):
        if response.status == 200:
            result = await response.json()
            logger.info(result)
            return result
        else:
            logger.error('name:%s uuid:%s response error:%s', self.name, req_uuid,
                         await response.json())
            return {}

    async def send_conversation(self, url: str = "https://api.dify.ai/v1/chat-messages", query: str = "",
                                conversation_id: str = "",
                                inputs=None, response_mode: str = 'blocking', interceptor=None):
        """发送聊天对话请求"""
        if inputs is None:
            inputs = {}
        headers = {
            'Authorization': 'Bearer ' + self.api_key,
            'Content-Type': 'application/json; charset=utf-8',
        }
        payload = {
            "inputs": inputs,
            "query": query,
            "conversation_id": conversation_id,
            "response_mode": response_mode,
            "user": self.user,
            "files": []
        }

        req_uuid = uuid.uuid1()
        logger.info('name:%s uuid:%s 聊天机器人对话参数:%s', self.name, req_uuid, payload)
        if 'blocking' in response_mode:
            async with aiohttp.ClientSession() as session:
                return await self.async_http_blocking(headers, payload, req_uuid, session, url)
        else:
            async with aiohttp.ClientSession() as session:
                return await self.async_http_stream(headers, payload, req_uuid, session, url, interceptor=interceptor)

    async def async_http_stream(self, headers, payload, req_uuid, session, url, interceptor):
        # 初始化一个空字符串用于组装最终响应
        assembled_response_answer = ''
        assembled_response_thought = ''

        conversation_id = ''
        start_time = time.time()
        first_token_time = 0
        usage = None
        burial_point = {}
        async with (session.post(url, json=payload, headers=headers) as response):
            # 处理流式响应
            async for line in response.content:
                if not line:
                    continue
                if first_token_time == 0:
                    first_token_time = time.time() - start_time
                # 解码字节数据为字符串
                chunk = line.decode('utf-8').strip()
                # 跳过空行
                if not chunk:
                    continue

                # 分割data:开头
                if chunk.startswith('data:'):
                    chunk = chunk[len('data:'):]
                logger.info("chunk:%s", chunk)
                # 解析JSON数据
                try:
                    chunk_data = json.loads(chunk)
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON: {chunk} error: {e} ")
                    continue

                logger.info("chunk:%s", chunk_data)
                if chunk_data.get('status') == 500 or chunk_data.get('event') == 'error' or chunk_data.get(
                        'status') == 'failed':
                    raise RetryException(message="发生错误,进行重试", code=500)
                # 检查chunk是否有有效的内容
                # agent_message
                if 'answer' in chunk_data:
                    conversation_id = chunk_data['conversation_id']
                    content = chunk_data['answer']
                    assembled_response_answer += content
                    if interceptor is not None:
                        interceptor(start_time=start_time, content=assembled_response_answer,
                                    burial_point=burial_point)
                        # agent_thought
                if 'thought' in chunk_data:
                    conversation_id = chunk_data['conversation_id']
                    content = chunk_data['thought']
                    assembled_response_thought += content
                if 'node_finished' in chunk_data.get('event') and chunk_data.get('data') is not None and chunk_data['data'].get('outputs') is not None and 'usage' in chunk_data['data'].get('outputs'):
                    usage = chunk_data['data']['outputs']['usage']
        if assembled_response_thought != '':
            response = assembled_response_thought
        else:
            response = assembled_response_answer
        data = {'conversation_id': conversation_id, 'answer': response,
                'first_token_time': "{:.2f}".format(first_token_time),
                'completed_time': "{:.2f}".format(time.time() - start_time),
                'usage': usage,
                'burial_point': burial_point}
        logger.info('name:%s uuid:%s 解析response参数:%s', self.name, req_uuid, data)
        return data

    async def async_http_blocking(self, headers, payload, req_uuid, session, url):
        async with session.post(url, json=payload, headers=headers) as response:
            if response.status == 200:
                result = DifyRequest.parse_conversation(self.name, req_uuid, await response.json())
                return result
            else:
                logger.error('name:%s uuid:%s response error:%s', self.name, req_uuid,
                             await response.json())
                return {}

    @staticmethod
    def parse_conversation(name, req_uuid, conversation_result):
        conversation_id = conversation_result.get('conversation_id')
        answer = conversation_result.get('answer')

        data = {
            "conversation_id": conversation_id,
            "answer": answer
        }
        logger.info('name:%s uuid:%s 解析response参数:%s', name, req_uuid, data)
        return data

    async def send_request_stream(self, req_uuid, response):
        """处理工作流流式响应"""
        # 初始化一个空字典用于存储最终输出
        outputs = {}
        
        start_time = time.time()
        first_token_time = 0
        usage = None
        
        if response.status == 200:
            async for line in response.content:
                if not line:
                    continue
                if first_token_time == 0:
                    first_token_time = time.time() - start_time
                    
                # 解码字节数据为字符串
                chunk = line.decode('utf-8').strip()
                # 跳过空行
                if not chunk:
                    continue
                    
                # 分割data:开头
                if chunk.startswith('data:'):
                    chunk = chunk[len('data:'):]
                logger.info("chunk:%s", chunk)
                
                # 解析JSON数据
                try:
                    chunk_data = json.loads(chunk)
                except json.JSONDecodeError as e:
                    logger.error(f"Error parsing JSON: {chunk} error: {e}")
                    continue
                    
                logger.info("chunk:%s", chunk_data)
                
                # 检查错误状态
                if chunk_data.get('status') == 500 or chunk_data.get('event') == 'error' or chunk_data.get('status') == 'failed':
                    raise RetryException(message="发生错误,进行重试", code=500)
                    
                # 处理工作流节点输出
                if chunk_data.get('event') == 'node_finished' and chunk_data.get('data') is not None:
                    node_data = chunk_data.get('data')
                    if node_data.get('outputs') is not None:
                        node_outputs = node_data.get('outputs')
                        # 合并输出
                        for key, value in node_outputs.items():
                            if key != 'usage':  # 单独处理usage
                                outputs[key] = value
                        # 处理usage信息
                        if 'usage' in node_outputs:
                            usage = node_outputs['usage']
        else:
            logger.error('name:%s uuid:%s streaming response error:%s', self.name, req_uuid,
                         await response.json())
            return {}
            
        # 构建最终返回数据
        data = {
            'data': {
                'outputs': outputs
            },
            'first_token_time': "{:.2f}".format(first_token_time),
            'completed_time': "{:.2f}".format(time.time() - start_time),
            'usage': usage
        }
        
        logger.info('name:%s uuid:%s 解析workflow streaming参数:%s', self.name, req_uuid, data)
        return data
