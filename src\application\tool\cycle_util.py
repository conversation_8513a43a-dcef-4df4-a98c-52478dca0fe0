﻿import logging
import traceback

from src.application.error.Exception import RetryException

logger = logging.getLogger()


def cycle_handle_without_conversation(rows, params, user, handle):
    datas = []
    for i, row in enumerate(rows):
        error_cycle = 0
        retry_index = 0
        data = None
        while retry_index < 3:
            try:
                data = handle(row=row, params=params, user=user)
                datas.extend(data)
                break
            except RetryException:
                retry_index = retry_index + 1
                logger.info("发生异常，当前重试次数:%s", retry_index)
            # 某一步异常不影响后续流程
            except Exception:
                ## 避免参数一直报错死循环
                error_cycle = error_cycle + 1
                traceback.print_exc()
                if error_cycle > 3:
                    break
        if data is None:
            logger.info(f"第{i}行执行异常,请核实数据")
            empty = ['执行异常']
            datas.append(empty)
    return datas
