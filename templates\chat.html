﻿<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8" />
    <title></title>
</head>
<body>
<h1>Audio</h1>

<button id="startRecordingButton">Start recording</button>
<button id="stopRecordingButton">Stop recording</button>
<button id="playButton">Play</button>

<div id="response"></div>
<input id="conversation_id" type="hidden"></input>

<div id="info"></div>

<script>
    var start = 0;
    var SAMPLE_RATE = 16000;
    const baseUrl = "http://************:5000";
    String.prototype.toHHMMSS = function() {
        var sec_num = parseInt(this, 10); // don't forget the second param
        var hours = Math.floor(sec_num / 3600);
        var minutes = Math.floor((sec_num - hours * 3600) / 60);
        var seconds = sec_num - hours * 3600 - minutes * 60;

        if (hours < 10) {
            hours = "0" + hours;
        }
        if (minutes < 10) {
            minutes = "0" + minutes;
        }
        if (seconds < 10) {
            seconds = "0" + seconds;
        }
        return hours + ":" + minutes + ":" + seconds;
    };

    var startRecordingButton = document.getElementById(
        "startRecordingButton"
    );
    var stopRecordingButton = document.getElementById("stopRecordingButton");
    var playButton = document.getElementById("playButton");

    var leftchannel = [];
    var rightchannel = [];
    var recorder = null;
    var recordingLength = 0;
    var volume = null;
    var mediaStream = null;
    var context = null;
    var blob = null;

    startRecordingButton.addEventListener("click", function() {
        start = new Date().getTime();

        // 使用新的 API
        navigator.mediaDevices.getUserMedia({
            audio: {
                sampleRate: 16000,
                channelCount: 2
            }
        })
            .then(function(e) {
                console.log("user consent");

                // 创建音频上下文
                window.AudioContext = window.AudioContext || window.webkitAudioContext;
                context = new AudioContext({sampleRate: 16000});

                // 创建音频节点
                mediaStream = context.createMediaStreamSource(e);

                var bufferSize = 2048;
                var numberOfInputChannels = 2;
                var numberOfOutputChannels = 2;
                if (context.createScriptProcessor) {
                    recorder = context.createScriptProcessor(
                        bufferSize,
                        numberOfInputChannels,
                        numberOfOutputChannels
                    );
                } else {
                    recorder = context.createJavaScriptNode(
                        bufferSize,
                        numberOfInputChannels,
                        numberOfOutputChannels
                    );
                }

                recorder.onaudioprocess = function(e) {
                    leftchannel.push(
                        new Float32Array(e.inputBuffer.getChannelData(0))
                    );
                    rightchannel.push(
                        new Float32Array(e.inputBuffer.getChannelData(1))
                    );
                    recordingLength += bufferSize;
                };

                // 连接录音机
                mediaStream.connect(recorder);
                recorder.connect(context.destination);
            })
            .catch(function(e) {
                console.error("Error accessing media devices.", e);
            });
    });

    stopRecordingButton.addEventListener("click", function() {
        reset()

        info.textContent = String((new Date().getTime()-start)/1000).toHHMMSS();
        window.AudioContext = window.AudioContext || window.webkitAudioContext;

        // stop recording
        recorder.disconnect(context.destination);
        mediaStream.disconnect(recorder);

        // we flat the left and right channels down
        // Float32Array[] => Float32Array
        var leftBuffer = flattenArray(leftchannel, recordingLength);
        var rightBuffer = flattenArray(rightchannel, recordingLength);
        // we interleave both channels together
        // [left[0],right[0],left[1],right[1],...]
        var interleaved = interleave(leftBuffer, rightBuffer);

        // we create our wav file
        var buffer = new ArrayBuffer(44 + interleaved.length * 2);
        var view = new DataView(buffer);

        // RIFF chunk descriptor
        writeUTFBytes(view, 0, "RIFF");
        view.setUint32(4, 44 + interleaved.length * 2, true);
        writeUTFBytes(view, 8, "WAVE");
        // FMT sub-chunk
        writeUTFBytes(view, 12, "fmt ");
        view.setUint32(16, 16, true); // chunkSize
        view.setUint16(20, 1, true); // wFormatTag
        view.setUint16(22, 2, true); // wChannels: stereo (2 channels)
        view.setUint32(24, 16000, true); // dwSamplesPerSec
        view.setUint32(28, 16000 * 4, true); // dwAvgBytesPerSec
        view.setUint16(32, 4, true); // wBlockAlign
        view.setUint16(34, 16, true); // wBitsPerSample
        // data sub-chunk
        writeUTFBytes(view, 36, "data");
        view.setUint32(40, interleaved.length * 2, true);

        // write the PCM samples
        var index = 44;
        var volume = 1;
        for (var i = 0; i < interleaved.length; i++) {
            view.setInt16(index, interleaved[i] * (0x7fff * volume), true);
            index += 2;
        }

        // our final blob
        blob = new Blob([view], {type: "audio/wav"});
        blobToPCM(blob, async (pcmData) => {

            const pcmBlob = new Blob([pcmData], {type: 'audio/pcm'});

            const formData = new FormData();
            formData.append('audio', pcmBlob, 'recording.pcm');
            const contentDiv = document.getElementById('response');
            contentDiv.innerHTML += "开始调用ASR+大模型：" + new Date() + "<br>";
            console.log("开始调用大模型：" + new Date())
            const response = await fetch(baseUrl + '/dialog/demo', {
                headers: new Headers({
                    'Access-Control-Allow-Origin': '*',
                    'Conversation-Id': document.getElementById('conversation_id').value
                }),
                method: 'POST',
                body: formData
            })

            const jsonString = await response.json();
            console.log("结束调用大模型：" + new Date())
            contentDiv.innerHTML += "结束调用ASR+大模型：" + new Date() + "<br>";
            if (jsonString.conversation_id === '') {
                contentDiv.innerHTML += "语音识别异常,请重新录制" + "<br>";
                return
            }
            console.info(jsonString)
            const jsonRelpy = JSON.stringify(jsonString)
            document.getElementById('conversation_id').value = jsonString["conversation_id"]
            // Append the fetched data to the content div
            contentDiv.innerHTML += "调用返回：" + JSON.stringify(jsonString["teacher_parse"]["reply"]) + "<br>";
            console.log("开始调用TTS：" + new Date())
            contentDiv.innerHTML += "开始调用TTS：" + new Date() + "<br>";
            const tts_response = await fetch(baseUrl + '/dialog/text_to_speech/demo', {
                headers: new Headers({
                    'Access-Control-Allow-Origin': '*',
                    'Content-Type': 'application/json'
                }),
                method: 'POST',
                body: jsonRelpy
            })
            console.log("结束调用TTS：" + new Date())
            contentDiv.innerHTML += "结束调用TTS：" + new Date() + "<br>";
            contentDiv.innerHTML += "<br>" + "<br>";
            const jsonData = await tts_response.json();
            // Extract the Base64 encoded audio data
            const base64Audio = jsonData.data; // Assume the JSON has a field "audioBase64"
            // Convert Base64 to Blob
            const outAudioBlob = base64ToBlob(base64Audio, 'audio/mpeg'); // Adjust MIME type as necessary

            // Create a URL for the audio Blob
            const audioUrl = URL.createObjectURL(outAudioBlob);

            // Create a new audio element and set its source
            const audioElement = new Audio(audioUrl);

            // Play the audio
            await audioElement.play();
        })

    });

    playButton.addEventListener("click", function() {
        if (blob == null) {
            return;
        }

        var url = window.URL.createObjectURL(blob);
        var audio = new Audio(url);
        audio.play();
    });

    function flattenArray(channelBuffer, recordingLength) {
        var result = new Float32Array(recordingLength);
        var offset = 0;
        for (var i = 0; i < channelBuffer.length; i++) {
            var buffer = channelBuffer[i];
            result.set(buffer, offset);
            offset += buffer.length;
        }
        return result;
    }

    function interleave(leftChannel, rightChannel) {
        var length = leftChannel.length + rightChannel.length;
        var result = new Float32Array(length);

        var inputIndex = 0;

        for (var index = 0; index < length; ) {
            result[index++] = leftChannel[inputIndex];
            result[index++] = rightChannel[inputIndex];
            inputIndex++;
        }
        return result;
    }

    function writeUTFBytes(view, offset, string) {
        for (var i = 0; i < string.length; i++) {
            view.setUint8(offset + i, string.charCodeAt(i));
        }
    }

    function reset() {
        leftchannel = [];
        rightchannel = [];
        recorder = null;
        recordingLength = 0;
        volume = null;
        mediaStream = null;
        context = null;
        blob = null;
    }

    // Utility function to convert Base64 to Blob
    function base64ToBlob(base64, mimeType) {
        const byteCharacters = atob(base64);
        const byteNumbers = new Array(byteCharacters.length);

        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        return new Blob([byteArray], { type: mimeType });
    }

    function blobToPCM(blob, callback) {
        callback(blob);
    }
</script>
</body>
</html>