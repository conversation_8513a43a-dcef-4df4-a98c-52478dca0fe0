import cv2
import os
import logging
import uuid
from typing import List, Dict, Union, Tuple, Optional

logger = logging.getLogger(__name__)

class VideoProcessor:
    """视频处理工具类，提供视频抽帧等功能"""
    
    @staticmethod
    def extract_frames_at_seconds(
        video_file: str, 
        seconds: List[int], 
        output_dir: str = "templates/frames"
    ) -> Dict[int, str]:
        """
        在指定的秒数位置从视频中抽取帧
        
        Args:
            video_file: 视频文件路径
            seconds: 需要抽取帧的秒数列表，例如[1, 5, 10]表示在第1秒、第5秒和第10秒抽取帧
            output_dir: 输出帧图像的目录，默认为templates/frames
            
        Returns:
            Dict[int, str]: 秒数到对应帧图像文件路径的映射，例如{1: "path/to/frame_1s.jpg"}
        """
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 打开视频文件
        cap = cv2.VideoCapture(video_file)
        if not cap.isOpened():
            logger.error(f"无法打开视频文件: {video_file}")
            return {}
        
        # 获取视频的FPS(每秒帧数)
        fps = cap.get(cv2.CAP_PROP_FPS)
        if fps <= 0:
            logger.error(f"无法获取视频FPS: {video_file}")
            return {}
        
        # 获取视频总帧数
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        video_duration = total_frames / fps
        
        # 存储结果
        result = {}
        
        # 为每个指定的秒数抽取帧
        for second in seconds:
            # 检查秒数是否在视频范围内
            if second < 0 or second > video_duration:
                logger.warning(f"指定的秒数 {second} 超出视频范围 (0-{video_duration}秒)")
                continue
            
            # 计算对应的帧位置
            frame_pos = int(second * fps)
            
            # 设置视频读取位置
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
            
            # 读取帧
            ret, frame = cap.read()
            if not ret:
                logger.warning(f"无法读取第 {second} 秒的帧")
                continue
            
            # 生成唯一的文件名
            file_id = uuid.uuid4()
            output_filename = f"frame_{second}s_{file_id}.jpg"
            output_path = os.path.join(output_dir, output_filename)
            
            # 保存帧为图像文件
            cv2.imwrite(output_path, frame)
            
            # 添加到结果
            result[second] = output_filename
        
        # 释放视频资源
        cap.release()
        
        return result
    
    @staticmethod
    def extract_frames_by_interval(
        video_file: str, 
        start_second: int, 
        end_second: int, 
        output_dir: str = "templates/frames"
    ) -> Dict[int, str]:
        """
        在视频的指定时间区间内抽取帧
        
        Args:
            video_file: 视频文件路径
            start_second: 开始秒数
            end_second: 结束秒数
            output_dir: 输出帧图像的目录，默认为templates/frames
            
        Returns:
            Dict[int, str]: 秒数到对应帧图像文件路径的映射
        """
        # 确保开始时间小于结束时间
        if start_second >= end_second:
            logger.error(f"开始时间 {start_second} 必须小于结束时间 {end_second}")
            return {}
        
        # 生成需要抽取的秒数列表
        seconds = list(range(start_second, end_second + 1))
        
        # 调用按秒抽帧的方法
        return VideoProcessor.extract_frames_at_seconds(video_file, seconds, output_dir) 