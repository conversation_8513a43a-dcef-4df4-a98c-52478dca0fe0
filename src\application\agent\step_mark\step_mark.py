﻿from src.application.agent.dify_request import DifyRequest
from src.application.error.Exception import RetryException
import asyncio
import logging

logger = logging.getLogger()


class StepMark:
    def __init__(self, user, api_key):
        self.api_key = api_key
        self.user = user

    def send_query(self, query, solution, answer, exercise, image, Aanswer, Eexercise):
        retry_index = 0
        while retry_index < 3:
            try:
                dify_request = DifyRequest(self.api_key, self.user, 'StepMark')
                param = {
                    'solution': solution,
                    'answer': answer,
                    'exercise': exercise,
                    'image': image,
                    'Aanswer': Aanswer,
                    'Eexercise': Eexercise
                }
                student_response = asyncio.run(
                    dify_request.send_conversation(query=query, inputs=param, response_mode='streaming'))
                return student_response
            except RetryException:
                retry_index = retry_index + 1
                logger.info("发生异常，当前重试次数:%s", retry_index)

        return None
