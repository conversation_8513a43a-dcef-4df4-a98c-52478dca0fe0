import logging
import time
import traceback
from io import BytesIO
from random import randint

from flask import send_file
from openpyxl import Workbook, load_workbook

from src.application.agent.solution_procedure.procedure import Procedure
from src.application.agent.step_mark.step_mark import StepMark
from openpyxl.styles import Font, Alignment
import re
logger = logging.getLogger()


def mark(file):
    analyze_data = analyze_excel(file)
    logger.info('解析后的文件内容:%s', analyze_data)
    user = '11111'

    # 模糊用户id
    user = randint(100000, 999999)
    datas = []
    for i, row in enumerate(analyze_data):
        try:
            conversation_id = ''
            process_query = ''
            data = [''] * 50
            datas.append(data)
            course_code = str(row[0] or '')
            sn = str(row[1])
            exercise = str(row[2] or '')
            a_solution_step = str(row[3] or '')
            exercise_solution_step = str(row[4] or '')
            topic = str(row[5] or '')
            instruct = str(row[6] or '')
            example_image = str(row[7] or '')
            example_a_image = str(row[8] or '')
            image = str(row[9] or '')
            solution_answer = str(row[10] or '')
            Aanswer = str(row[11] or '')
            Eexercise = str(row[12] or '')
            index = prepare_base_excel_data(index=0, data=data, course_code=course_code, sn=sn, exercise=exercise,
                                            topic=topic,
                                            solution_step=exercise_solution_step, Example_image=example_image,
                                            Example_a_image=example_a_image, image=image,
                                            solution_answer=solution_answer)
            ## 基础数据后的有效数据

            max_cycle = 4
            ## 最后一个步骤
            last_index = 43
            process_query = a_solution_step
            for cycle in range(max_cycle):
                procedure = Procedure(user, 'app-x6TUJZnkVwAzP8tNG8fi4jwD')
                procedure_response = procedure.send_query_v2(conversation_id=conversation_id, query=process_query,
                                                             example=exercise_solution_step, example_a=topic, s=course_code,
                                                             reflect='', reflect2='',
                                                             instruct=instruct, Example_image=example_image,
                                                             Example_a_image=example_a_image)
                logger.info(procedure_response)
                answer = procedure_response['answer']
                conversation_id = procedure_response['conversation_id']
                data[last_index] = answer
                ## 最后一次只需要答案
                if cycle == max_cycle - 1:
                    data[index + 1] = answer
                    break

                step_mark = StepMark(user, 'app-yqvjFjZO1uj9pH7AKTwgjq4Q')
                step_answer = step_mark.send_query(query=answer, solution=exercise_solution_step, answer=solution_answer,
                                                   exercise=exercise, image=image, Aanswer=Aanswer, Eexercise=Eexercise)
                step_answer_text = step_answer.get('answer')
                parse_answer = parse_step_mark_answer(step_answer_text)
                suggest_score = parse_answer['step_suggest']['score']
                process_query = parse_answer['step_suggest']['message']
                index = prepare_excel_data(data=data, index=index, answer=answer, step_answer=parse_answer)
                if suggest_score == '0':
                    break
        # 某一步异常不影响后续流程
        except Exception:
            traceback.print_exc()
    return export_excel(datas)


def prepare_base_excel_data(index, data, course_code, sn, exercise, topic, solution_step, Example_a_image,
                            Example_image, image, solution_answer):
    data[index] = course_code
    data[index + 1] = sn
    data[index + 2] = exercise
    data[index + 3] = solution_step
    data[index + 4] = topic
    data[index + 5] = Example_a_image
    data[index + 6] = Example_image
    data[index + 7] = image
    data[index + 8] = solution_answer
    return index + 8


def prepare_excel_data(data, index, answer, step_answer):
    data[index + 1] = answer
    data[index + 2] = step_answer['resolve']['score']
    data[index + 3] = step_answer['resolve']['message']
    data[index + 4] = step_answer['step_integrity']['score']
    data[index + 5] = step_answer['step_integrity']['message']
    data[index + 6] = step_answer['step_format']['score']
    data[index + 7] = step_answer['step_format']['message']
    data[index + 8] = step_answer['step_perfect']['score']
    data[index + 9] = step_answer['step_perfect']['message']
    data[index + 10] = step_answer['step_suggest']['score']
    data[index + 11] = step_answer['step_suggest']['message']
    return index + 11


def export_excel(data):
    wb = Workbook()
    ws = wb.active
    # 添加表头
    get_headers(ws)

    # 添加数据
    for i, item in enumerate(data):
        logger.info('打印excel数据第%s行,data: %s', i, item)
        ws.append(item)

    # 创建一个内存文件对象
    excel_buffer = BytesIO()
    wb.save(excel_buffer)
    excel_buffer.seek(0)

    # 返回 Excel 文件
    return send_file(
        excel_buffer,
        as_attachment=True,
        download_name='exported_data_step_mark_' + str(time.time()) + '.xlsx',
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )


def parse_step_mark_answer(answer):
    matches = re.findall(r'\|分析过程：([\s\S]*?)\|评分结果：\|(\d)\|', answer)
    data = {}
    # 打印结果
    for i, (analysis, score) in enumerate(matches, 1):
        if i == 1:
            data['resolve'] = {}
            data['resolve']['message'] = analysis
            data['resolve']['score'] = score
        if i == 2:
            data['step_integrity'] = {}
            data['step_integrity']['message'] = analysis
            data['step_integrity']['score'] = score
        if i == 3:
            data['step_format'] = {}
            data['step_format']['message'] = analysis
            data['step_format']['score'] = score
        if i == 4:
            data['step_perfect'] = {}
            data['step_perfect']['message'] = analysis
            data['step_perfect']['score'] = score

    matches2 = re.findall(r'\|优化建议：([\s\S]*?)\|评分结果：\|(\d)\|', answer)

    for i, (analysis, score) in enumerate(matches2, 1):
        data['step_suggest'] = {}
        data['step_suggest']['message'] = analysis
        data['step_suggest']['score'] = score
    logger.info(f"解析后的数据{data}")
    return data


def analyze_excel(file):
    """解析excel文件，封装成指定的数据结构."""

    # 读取 Excel 文件
    workbook = load_workbook(file)

    sheet = workbook['Sheet1']
    # 读取数据
    rows = []
    for index, row in enumerate(sheet.iter_rows(values_only=True)):
        if index == 0:
            continue
        rows.append(row)
    return rows


def convert_to_excel_column(column, increment):
    # Convert the column label to a number
    column = column.upper()
    start_index = 0
    for i, char in enumerate(reversed(column)):
        start_index += (ord(char) - ord('A') + 1) * (26 ** i)

    # Increment the index by the given number
    target_index = start_index + increment

    # Convert the new index back to an Excel column label
    column_name = ''
    while target_index > 0:
        target_index, remainder = divmod(target_index - 1, 26)
        column_name = chr(remainder + ord('A')) + column_name

    return column_name


def get_headers(ws):
    # 设置复杂合并的表头
    # 合并单元格并填充表头
    ws.merge_cells('A1:A3')
    ws['A1'] = "课件编码"

    ws.merge_cells('B1:B3')
    ws['B1'] = "序号"
    ws.merge_cells('C1:C3')
    ws['C1'] = "课后习题文本"
    ws.merge_cells('D1:D3')
    ws['D1'] = "例题解题步骤"
    ws.merge_cells('E1:E3')
    ws['E1'] = "例题题干"
    ws.merge_cells('F1:F3')
    ws['F1'] = "例题题干配图描述"
    ws.merge_cells('G1:G3')
    ws['G1'] = "例题解题步骤图片描述"
    ws.merge_cells('H1:H3')
    ws['H1'] = "例题配图描述"
    ws.merge_cells('I1:I3')
    ws['I1'] = "家长版正确答案"
    char_start = 'J'
    range_size = 11
    cycle_size = 3
    for i in range(cycle_size):
        ws.merge_cells(char_start + '1' + ':' + convert_to_excel_column(char_start, range_size - 1) + '1')
        ws[char_start + '1'] = "第" + str(i + 1) + "次打分"
        ws[char_start + '2'] = "第" + str(i + 1) + "次"
        ws.merge_cells(convert_to_excel_column(char_start, 1) + '2' + ':' + convert_to_excel_column(char_start, + 2) + '2')
        ws[convert_to_excel_column(char_start, + 1) + '2'] = "碗豆精灵解答是否正确"
        ws.merge_cells(convert_to_excel_column(char_start, 3) + '2' + ':' + convert_to_excel_column(char_start, 4) + '2')
        ws[convert_to_excel_column(char_start, 3) + '2'] = "解题步骤是否完整"
        ws.merge_cells(convert_to_excel_column(char_start, 5) + '2' + ':' + convert_to_excel_column(char_start, 6) + '2')
        ws[convert_to_excel_column(char_start, 5) + '2'] = "解题步骤是否完美"
        ws.merge_cells(convert_to_excel_column(char_start, 7) + '2' + ':' + convert_to_excel_column(char_start, 8) + '2')
        ws[convert_to_excel_column(char_start, 7) + '2'] = "解题步骤格式是否正确"
        ws.merge_cells(convert_to_excel_column(char_start, 9) + '2' + ':' + convert_to_excel_column(char_start, 10) + '2')
        ws[convert_to_excel_column(char_start, 9) + '2'] = "<老师生成的解题步骤 >是否需要优化"
        ws[char_start + '3'] = "生成步骤"
        index = 0
        for j in range(4):
            ws[convert_to_excel_column(char_start, 1 + index) + '3'] = "评分结果"
            ws[convert_to_excel_column(char_start, 2 + index) + '3'] = "分析过程"
            index += 2
        ws[convert_to_excel_column(char_start, 1 + index) + '3'] = "评分结果"
        ws[convert_to_excel_column(char_start, 2 + index) + '3'] = "优化建议"
        char_start = convert_to_excel_column(char_start, range_size)

    ws.merge_cells(char_start + '1' + ':' + char_start + '2')
    ws[char_start + '1'] = "第" + str(cycle_size + 1) + "次"
    ws[char_start + '3'] = "生成步骤"
    char_start = convert_to_excel_column(char_start, 1)
    ws.merge_cells(char_start + '1' + ':' + char_start + '3')
    ws[char_start + '1'] = "最后一次生成步骤"

    # 设置样式：居中和加粗
    for row in ws.iter_rows(min_row=1, max_row=2, min_col=1, max_col=60):
        for cell in row:
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.font = Font(bold=True)
    pass