from logging.config import dictConfig

dictConfig({
    'version': 1,
    'formatters': {'default': {
        'format': '[%(asctime)s] %(threadName)s %(levelname)s in %(module)s %(lineno)s: %(message)s',
    }},
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "INFO",
            "formatter": "default",
        },
        "file": {
            "class": "logging.FileHandler",
            "level": "INFO",
            "filename": "./dify-agent.log",
            "formatter": "default",
            'encoding': 'utf-8'
        }
    },
    "loggers": {
        "console_logger": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "file_logger": {
            "handlers": ["file"],
            "level": "INFO",
            "propagate": False,
        }
    },
    'root': {
        'level': 'INFO',
        'handlers': ['console', 'file']
    },
})



def init():
    pass
