"""高级Excel处理工具类

提供更高级的Excel处理抽象，包括：
- 统一的文件处理接口
- 通用的数据处理方法
- 标准化的错误处理
- 性能优化的批处理
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable, Union
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
from random import randint

from src.application.tool.excel_processor import ExcelProcessor, DialogExcelProcessor, SimpleExcelProcessor

logger = logging.getLogger(__name__)


class BaseExcelHandler(ABC):
    """Excel处理器基类
    
    定义了Excel处理的标准接口和通用方法。
    """
    
    def __init__(self):
        """初始化处理器"""
        self.processor: Optional[ExcelProcessor] = None
        self.user_id: int = randint(100000, 999999)  # 模糊用户ID
    
    @abstractmethod
    def process_file(self, file, **kwargs) -> bytes:
        """处理Excel文件的抽象方法
        
        Args:
            file: Excel文件
            **kwargs: 额外参数
            
        Returns:
            处理后的Excel文件字节数据
        """
        pass
    
    def _log_processing_info(self, columns: List[str], rows_count: int):
        """记录处理信息
        
        Args:
            columns: 列名列表
            rows_count: 行数
        """
        logger.info('Excel文件处理信息: columns=%s, rows_count=%d, user_id=%d', 
                   columns, rows_count, self.user_id)
    
    def _prepare_params(self, columns: List[str], **extra_params) -> Dict[str, Any]:
        """准备处理参数
        
        Args:
            columns: 列名列表
            **extra_params: 额外参数
            
        Returns:
            处理参数字典
        """
        params = {'columns': columns}
        params.update(extra_params)
        return params


class CycleExcelHandler(BaseExcelHandler):
    """循环处理Excel处理器
    
    用于处理需要循环处理每行数据的Excel文件。
    """
    
    def __init__(self, max_workers: int = 5):
        """初始化循环处理器
        
        Args:
            max_workers: 最大工作线程数
        """
        super().__init__()
        self.max_workers = max_workers
    
    def process_file(self, file, handle_func: Callable, headers: List[str], **kwargs) -> bytes:
        """处理Excel文件
        
        Args:
            file: Excel文件
            handle_func: 行处理函数
            headers: 输出表头
            **kwargs: 额外参数
            
        Returns:
            处理后的Excel文件字节数据
        """
        # 初始化处理器
        self.processor = ExcelProcessor()
        self.processor.load_file(file)
        
        # 获取数据
        columns = self.processor.get_columns()
        rows = self.processor.get_rows_as_list(skip_header=True)
        
        self._log_processing_info(columns, len(rows))
        
        # 准备参数
        params = self._prepare_params(columns, **kwargs)
        
        # 处理数据
        datas = self._process_rows_with_cycle(rows, params, handle_func)
        
        # 导出结果
        return self.processor.export_to_excel(headers=headers, data=datas)
    
    def _process_rows_with_cycle(self, rows: List[List[Any]], params: Dict[str, Any], 
                                handle_func: Callable) -> List[List[Any]]:
        """使用循环处理行数据
        
        Args:
            rows: 行数据列表
            params: 处理参数
            handle_func: 处理函数
            
        Returns:
            处理结果列表
        """
        from src.application.tool.cycle_util import cycle_handle_without_conversation
        
        return cycle_handle_without_conversation(
            rows=rows, 
            params=params, 
            user=self.user_id, 
            handle=handle_func
        )


class AsyncExcelHandler(BaseExcelHandler):
    """异步Excel处理器
    
    用于处理需要异步/并发处理的Excel文件。
    """
    
    def __init__(self, max_workers: int = 5):
        """初始化异步处理器
        
        Args:
            max_workers: 最大工作线程数
        """
        super().__init__()
        self.max_workers = max_workers
    
    def process_file(self, file, process_func: Callable, headers: List[str], 
                    sort_key: Optional[Callable] = None, **kwargs) -> bytes:
        """异步处理Excel文件
        
        Args:
            file: Excel文件
            process_func: 行处理函数
            headers: 输出表头
            sort_key: 排序键函数
            **kwargs: 额外参数
            
        Returns:
            处理后的Excel文件字节数据
        """
        # 初始化处理器
        self.processor = ExcelProcessor()
        self.processor.load_file(file)
        
        # 获取数据
        columns = self.processor.get_columns()
        rows = self.processor.get_rows_as_list(skip_header=True)
        
        self._log_processing_info(columns, len(rows))
        
        # 异步处理数据
        datas = self._process_rows_async(rows, columns, process_func, **kwargs)
        
        # 排序结果
        if sort_key:
            datas.sort(key=sort_key)
        
        # 导出结果
        return self.processor.export_to_excel(headers=headers, data=datas)
    
    def _process_rows_async(self, rows: List[List[Any]], columns: List[str], 
                           process_func: Callable, **kwargs) -> List[List[Any]]:
        """异步处理行数据
        
        Args:
            rows: 行数据列表
            columns: 列名列表
            process_func: 处理函数
            **kwargs: 额外参数
            
        Returns:
            处理结果列表
        """
        datas = []
        data_lock = Lock()
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_index = {}
            for i, row in enumerate(rows):
                future = executor.submit(
                    process_func, 
                    '', i + 2, row, columns, self.user_id, **kwargs
                )
                future_to_index[future] = i + 2
            
            # 收集结果
            for future in as_completed(future_to_index):
                row_index = future_to_index[future]
                try:
                    result = future.result()
                    if result is not None:
                        with data_lock:
                            datas.append(result)
                            logger.info(f"第{row_index}行处理完成")
                except Exception as e:
                    logger.error(f"第{row_index}行处理失败: {e}")
        
        return datas


class DialogExcelHandler(BaseExcelHandler):
    """对话Excel处理器
    
    专门处理包含多个工作表的对话场景Excel文件。
    """
    
    def process_file(self, file, **kwargs) -> Dict[str, Any]:
        """处理对话Excel文件
        
        Args:
            file: Excel文件
            **kwargs: 额外参数
            
        Returns:
            解析后的数据结构
        """
        # 初始化处理器
        self.processor = DialogExcelProcessor()
        
        # 解析文件
        result = self.processor.parse_dialog_excel(file)
        
        logger.info('对话Excel文件处理完成: session_num=%s, cycle_num=%s', 
                   result.get('session_num'), result.get('cycle_num'))
        
        return result
    
    def export_results(self, headers: List[str], data: List[List[Any]]) -> bytes:
        """导出处理结果
        
        Args:
            headers: 表头列表
            data: 数据列表
            
        Returns:
            Excel文件字节数据
        """
        if self.processor is None:
            self.processor = DialogExcelProcessor()
        
        return self.processor.export_to_excel(headers=headers, data=data)


class SimpleExcelHandler(BaseExcelHandler):
    """简单Excel处理器
    
    处理单一工作表的简单Excel文件。
    """
    
    def process_file(self, file, sheet_name: str = 'Sheet1', **kwargs) -> List[List[Any]]:
        """处理简单Excel文件
        
        Args:
            file: Excel文件
            sheet_name: 工作表名称
            **kwargs: 额外参数
            
        Returns:
            行数据列表
        """
        # 初始化处理器
        self.processor = SimpleExcelProcessor()
        
        # 解析文件
        rows = self.processor.parse_simple_excel(file, sheet_name)
        
        logger.info('简单Excel文件处理完成: rows_count=%d', len(rows))
        
        return rows
    
    def export_results(self, headers: List[str], data: List[List[Any]]) -> bytes:
        """导出处理结果
        
        Args:
            headers: 表头列表
            data: 数据列表
            
        Returns:
            Excel文件字节数据
        """
        if self.processor is None:
            self.processor = SimpleExcelProcessor()
        
        return self.processor.export_to_excel(headers=headers, data=data)


# 工厂函数，用于创建合适的处理器
def create_excel_handler(handler_type: str, **kwargs) -> BaseExcelHandler:
    """创建Excel处理器
    
    Args:
        handler_type: 处理器类型 ('cycle', 'async', 'dialog', 'simple')
        **kwargs: 处理器参数
        
    Returns:
        Excel处理器实例
    """
    handlers = {
        'cycle': CycleExcelHandler,
        'async': AsyncExcelHandler,
        'dialog': DialogExcelHandler,
        'simple': SimpleExcelHandler
    }
    
    if handler_type not in handlers:
        raise ValueError(f"不支持的处理器类型: {handler_type}")
    
    return handlers[handler_type](**kwargs)


# 便捷函数
def process_cycle_excel(file, handle_func: Callable, headers: List[str], **kwargs) -> bytes:
    """便捷函数：处理循环Excel
    
    Args:
        file: Excel文件
        handle_func: 处理函数
        headers: 表头列表
        **kwargs: 额外参数
        
    Returns:
        Excel文件字节数据
    """
    handler = CycleExcelHandler()
    return handler.process_file(file, handle_func, headers, **kwargs)


def process_async_excel(file, process_func: Callable, headers: List[str], 
                       sort_key: Optional[Callable] = None, **kwargs) -> bytes:
    """便捷函数：处理异步Excel
    
    Args:
        file: Excel文件
        process_func: 处理函数
        headers: 表头列表
        sort_key: 排序键函数
        **kwargs: 额外参数
        
    Returns:
        Excel文件字节数据
    """
    handler = AsyncExcelHandler()
    return handler.process_file(file, process_func, headers, sort_key, **kwargs)


def process_dialog_excel(file) -> Dict[str, Any]:
    """便捷函数：处理对话Excel
    
    Args:
        file: Excel文件
        
    Returns:
        解析后的数据结构
    """
    handler = DialogExcelHandler()
    return handler.process_file(file)


def process_simple_excel(file, sheet_name: str = 'Sheet1') -> List[List[Any]]:
    """便捷函数：处理简单Excel
    
    Args:
        file: Excel文件
        sheet_name: 工作表名称
        
    Returns:
        行数据列表
    """
    handler = SimpleExcelHandler()
    return handler.process_file(file, sheet_name)