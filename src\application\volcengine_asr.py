﻿import base64
import json
import time
import uuid
import requests


def submit_task(data):

    submit_url = "https://openspeech.bytedance.com/api/v3/auc/bigmodel/submit"

    task_id = str(uuid.uuid4())

    headers = {
        "X-Api-App-Key": appid,
        "X-Api-Access-Key": token,
        "X-Api-Resource-Id": "volc.bigasr.auc",
        "X-Api-Request-Id": task_id,
        "X-Api-Sequence": "-1"
    }

    request = {
        "user": {
            "uid": "Experience Center"
        },
        "audio": {
            "data": str(data.decode('utf-8')),
            "format": "wav"
        },
        "request": {
            "use_punc": "True",
            "use_itn": "True",
            "modal_name": "bigmodel"
        }
    }

    # request = {
    #     "user": {
    #         "uid": "fake_uid"
    #     },
    #     "audio": {
    #         "url":  str(data.decode('utf-8')),
    #         "format": "wav"
    #     },
    #     "request": {
    #         "use_punc": "True",
    #         "use_itn": "True",
    #         "modal_name": "bigmodel"
    #     }
    # }
    print(f'Submit task id: {task_id}')
    response = requests.post(submit_url, data=json.dumps(request), headers=headers)
    if 'X-Api-Status-Code' in response.headers and response.headers["X-Api-Status-Code"] == "20000000":
        print(f'Submit task response header X-Api-Status-Code: {response.headers["X-Api-Status-Code"]}')
        print(f'Submit task response header X-Api-Message: {response.headers["X-Api-Message"]}')
        print(f'Submit task response header X-Tt-Logid: {response.headers["X-Tt-Logid"]}\n')
    else:
        print(f'Submit task failed and the response headers are: {response.headers}')
        print(f'Submit task failed and the response text are: {response.text}')
        exit(1)
    return task_id


def query_task(task_id):
    query_url = "https://openspeech.bytedance.com/api/v3/auc/bigmodel/query"

    headers = {
        "X-Api-App-Key": appid,
        "X-Api-Access-Key": token,
        "X-Api-Resource-Id": "volc.bigasr.auc",
        "X-Api-Request-Id": task_id,
    }

    response = requests.post(query_url, json.dumps({}), headers=headers)
    if 'X-Api-Status-Code' in response.headers:
        print(f'Query task response header X-Api-Message: {response.headers["X-Api-Message"]}')
        print(f'Query task response header X-Tt-Logid: {response.headers["X-Tt-Logid"]}\n')
    else:
        print(f'Query task failed and the response headers are: {response.headers}')
        exit(1)
    return response


def run_asr(file):
    # 分块读取音频数据，并发送给服务器
    audio_data = file.read()
    base64_encoded = base64.b64encode(audio_data)
    task_id = submit_task(base64_encoded)
    while True:
        query_response = query_task(task_id)
        code = query_response.headers['X-Api-Status-Code']
        if code == '20000000':  # task finished
            json_data = query_response.json()
            return json_data['result']['text']
        elif code != '20000001' and code != '20000002':  # task failed
            print("FAILED!")
            return ""
        time.sleep(1)



# 填入控制台获取的app id和access token
appid = "5273836321"
token = "dmcLMHpAwYOKygUbE6GkcXh_QCmd8Qly"
