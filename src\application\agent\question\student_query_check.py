from src.application.agent.dify_request import DifyRequest
import asyncio


class StudentQueryCheck:
    def __init__(self, user, api_key):
        self.api_key = api_key
        self.user = user

    def check_query(self, question, scence, history_dialog, exercise):
        dify_request = DifyRequest(self.api_key, self.user, 'StudentQueryCheck')
        param = {
            "question": question,
            "scence": scence,
            'history_dialog': history_dialog,
            'exercise': exercise
        }
        student_response = asyncio.run(dify_request.send_conversation(query='.', conversation_id='', inputs=param))
        answer = student_response.get('answer')
        if answer is None:
            return {}
        else:
            return answer
