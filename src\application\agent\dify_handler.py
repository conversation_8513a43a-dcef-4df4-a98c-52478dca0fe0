﻿from src.application.agent.dify_request import DifyRequest


class DifyHandler:
    def __init__(self, user, api_key, name):
        self.api_key = api_key
        self.user = user
        self.name = name

    async def send_conversation(self, query, params, response_mode: str = 'blocking'):
        dify_request = DifyRequest(self.api_key, self.user, self.name)
        return await dify_request.send_conversation(query=query, inputs=params, response_mode=response_mode)

    async def send_request(self, params):
        dify_request = DifyRequest(self.api_key, self.user, self.name)
        return await dify_request.send_request(inputs=params)
